#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
检查基本的导入和配置
"""

import sys
import traceback

def test_imports():
    """测试导入"""
    print("🔍 测试导入...")
    
    try:
        from crawler_config import get_config
        print("✅ crawler_config 导入成功")
        
        config = get_config()
        print(f"✅ 配置加载成功，包含 {len(config)} 个配置项")
        
        from test_crawler import AmazonCrawler
        print("✅ AmazonCrawler 导入成功")
        
        crawler = AmazonCrawler(config)
        print("✅ AmazonCrawler 实例创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        traceback.print_exc()
        return False

def test_database_connection():
    """测试数据库连接"""
    print("\n🔍 测试数据库连接...")
    
    try:
        from crawler_config import get_config
        import pymysql
        
        config = get_config()
        conn = pymysql.connect(**config['db'])
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM zz_amazon_page_tasks")
        count = cursor.fetchone()[0]
        
        print(f"✅ 数据库连接成功，zz_amazon_page_tasks 表有 {count} 条记录")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        traceback.print_exc()
        return False

def test_basic_extraction():
    """测试基本的数据提取"""
    print("\n🔍 测试基本数据提取...")
    
    try:
        from bs4 import BeautifulSoup
        
        # 模拟HTML
        html = '''
        <div data-asin="B123456789">
            <h2><a href="/dp/B123456789"><span>Test Product Title</span></a></h2>
            <span class="a-price"><span class="a-offscreen">$19.99</span></span>
            <i aria-label="Prime delivery">Prime</i>
        </div>
        '''
        
        soup = BeautifulSoup(html, 'html.parser')
        container = soup.select_one('[data-asin]')
        
        # 测试ASIN提取
        asin = container.get('data-asin', '')
        print(f"ASIN: {asin}")
        
        # 测试标题提取
        title_elem = container.select_one('h2 a span')
        title = title_elem.get_text(strip=True) if title_elem else ''
        print(f"标题: {title}")
        
        # 测试链接提取
        link_elem = container.select_one('h2 a')
        href = link_elem.get('href', '') if link_elem else ''
        print(f"链接: {href}")
        
        # 测试Prime信息提取
        prime_elem = container.select_one('[aria-label*="Prime"]')
        prime_info = prime_elem.get('aria-label', '') if prime_elem else ''
        print(f"Prime: {prime_info}")
        
        if asin and title and href and prime_info:
            print("✅ 基本数据提取功能正常")
            return True
        else:
            print("❌ 基本数据提取有问题")
            return False
        
    except Exception as e:
        print(f"❌ 基本数据提取测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 简单测试脚本")
    print("=" * 40)
    
    try:
        # 1. 测试导入
        import_success = test_imports()
        
        # 2. 测试数据库连接
        db_success = test_database_connection()
        
        # 3. 测试基本数据提取
        extraction_success = test_basic_extraction()
        
        print("\n📊 测试结果:")
        print(f"  导入测试: {'✅' if import_success else '❌'}")
        print(f"  数据库测试: {'✅' if db_success else '❌'}")
        print(f"  提取测试: {'✅' if extraction_success else '❌'}")
        
        if import_success and db_success and extraction_success:
            print("\n✅ 所有基本测试通过")
            return 0
        else:
            print("\n❌ 部分测试失败")
            return 1
        
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
