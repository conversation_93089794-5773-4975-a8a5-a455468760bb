
def extract_single_product_improved(self, container):
    """改进的单个商品数据提取"""
    try:
        product_data = {}
        
        # 提取ASIN
        asin = container.get('data-asin', '')
        if not asin:
            return None
        product_data['asin'] = asin
        
        # 提取标题 - 使用多个选择器
        title_selectors = [
            'h2 a span[aria-label]',
            'h2 a span',
            '[data-cy="title-recipe"] span',
            'h2 span',
            '.a-size-base-plus',
            '.a-size-mini span'
        ]
        
        title = ''
        for selector in title_selectors:
            title_elem = container.select_one(selector)
            if title_elem:
                title = title_elem.get_text(strip=True)
                if title:  # 确保不是空字符串
                    break
        product_data['title'] = title
        
        # 提取商品链接 - 使用多个选择器
        link_selectors = [
            'h2 a[href*="/dp/"]',
            'a[href*="/dp/"]',
            'h2 a',
            '.a-link-normal[href*="/dp/"]',
            'a[href*="/gp/product/"]'
        ]
        
        product_url = ''
        for selector in link_selectors:
            link_elem = container.select_one(selector)
            if link_elem and link_elem.get('href'):
                href = link_elem['href']
                if href.startswith('/'):
                    product_url = f"https://www.amazon.com{href}"
                else:
                    product_url = href
                if product_url:  # 确保不是空字符串
                    break
        product_data['product_url'] = product_url
        
        # 提取Prime信息 - 使用多个选择器
        prime_selectors = [
            'i[aria-label*="Prime"]',
            '[aria-label*="Prime"]',
            '.a-icon-prime',
            'span[aria-label*="Prime"]',
            '.s-prime',
            '[data-testid*="prime"]',
            'i.a-icon.a-icon-prime'
        ]
        
        prime_info = ''
        for selector in prime_selectors:
            prime_elem = container.select_one(selector)
            if prime_elem:
                prime_text = prime_elem.get('aria-label', '') or prime_elem.get_text(strip=True)
                if prime_text and 'prime' in prime_text.lower():
                    prime_info = prime_text[:255]
                    break
        
        # 如果没找到Prime元素，检查文本内容
        if not prime_info:
            container_text = container.get_text()
            if 'prime' in container_text.lower():
                # 尝试提取Prime相关文本
                prime_patterns = [
                    r'(Prime\s+\w+)',
                    r'(FREE\s+.*?Prime)',
                    r'(Prime\s+delivery)'
                ]
                for pattern in prime_patterns:
                    match = re.search(pattern, container_text, re.IGNORECASE)
                    if match:
                        prime_info = match.group(1)[:255]
                        break
        
        product_data['prime_info'] = prime_info
        
        # ... 其他字段提取保持不变 ...
        
        return product_data
        
    except Exception as e:
        self.logger.error(f"提取商品数据失败: {e}")
        return None
