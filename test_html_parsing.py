#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试HTML文件解析
验证 doc/list.html 和 doc/list2.html 的解析是否正确
"""

import os
import sys
from bs4 import BeautifulSoup
from decimal import Decimal
import re
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test_crawler import AmazonCrawler
from crawler_config import get_config

def load_html_file(file_path):
    """加载HTML文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        return content
    except Exception as e:
        print(f"❌ 加载文件失败 {file_path}: {e}")
        return None

def test_html_file_parsing(file_path, file_name):
    """测试单个HTML文件的解析"""
    print(f"\n🔍 测试 {file_name} 解析")
    print("=" * 60)
    
    # 加载HTML文件
    html_content = load_html_file(file_path)
    if not html_content:
        return False
    
    print(f"✅ 成功加载 {file_name} ({len(html_content)} 字符)")
    
    try:
        # 创建爬虫实例
        config = get_config()
        crawler = AmazonCrawler(config)
        
        # 解析HTML
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 使用爬虫的方法提取商品
        products = crawler.extract_products_from_page(soup)
        
        if not products:
            print("❌ 没有提取到商品数据")
            
            # 调试：查看页面结构
            print("\n🔍 调试信息:")
            
            # 查找可能的商品容器
            selectors = [
                '[data-component-type="s-search-result"]',
                'div[data-asin]',
                '.s-result-item[data-asin]',
                '[data-asin]:not([data-asin=""])'
            ]
            
            for selector in selectors:
                containers = soup.select(selector)
                print(f"  选择器 '{selector}': 找到 {len(containers)} 个容器")
                
                if containers:
                    # 显示第一个容器的部分内容
                    first_container = containers[0]
                    asin = first_container.get('data-asin', 'N/A')
                    print(f"    第一个容器 ASIN: {asin}")
                    
                    # 查看标题
                    title_elem = first_container.select_one('h2 a span, h2 span')
                    title = title_elem.get_text(strip=True) if title_elem else 'N/A'
                    print(f"    标题: {title[:50]}...")
                    break
            
            return False
        
        print(f"✅ 成功提取到 {len(products)} 个商品")
        
        # 分析提取结果
        print(f"\n📊 {file_name} 数据分析:")
        
        # 统计各字段的完整性
        stats = {
            'asin': sum(1 for p in products if p.get('asin')),
            'title': sum(1 for p in products if p.get('title')),
            'product_url': sum(1 for p in products if p.get('product_url')),
            'prime_info': sum(1 for p in products if p.get('prime_info')),
            'price': sum(1 for p in products if p.get('price', 0) > 0),
            'rating': sum(1 for p in products if p.get('rating', 0) > 0),
            'review_count': sum(1 for p in products if p.get('review_count', 0) > 0),
            'image_url': sum(1 for p in products if p.get('image_url')),
            'bought_num': sum(1 for p in products if p.get('bought_num', 0) > 0),
            'is_sponsored': sum(1 for p in products if p.get('is_sponsored', 0) > 0)
        }
        
        total = len(products)
        
        print(f"  总商品数: {total}")
        print(f"  字段完整性:")
        for field, count in stats.items():
            percentage = (count / total * 100) if total > 0 else 0
            status = "✅" if percentage > 80 else "⚠️" if percentage > 50 else "❌"
            print(f"    {field}: {count}/{total} ({percentage:.1f}%) {status}")
        
        # 显示前3个商品的详细信息
        print(f"\n📋 {file_name} 商品详情 (前3个):")
        
        for i, product in enumerate(products[:3], 1):
            print(f"\n--- 商品 {i} ---")
            print(f"ASIN: {product.get('asin', 'N/A')}")
            print(f"标题: {product.get('title', 'N/A')[:60]}...")
            
            url = product.get('product_url', 'N/A')
            url_status = "✅" if url and url != 'N/A' else "❌"
            print(f"URL: {url_status} {url[:60]}..." if url != 'N/A' else f"URL: {url_status} (空)")
            
            prime = product.get('prime_info', 'N/A')
            prime_status = "✅" if prime and prime != 'N/A' else "❌"
            print(f"Prime: {prime_status} {prime}" if prime != 'N/A' else f"Prime: {prime_status} (空)")
            
            print(f"价格: ${product.get('price', 0)}")
            print(f"原价: ${product.get('original_price', 0)}")
            print(f"评分: {product.get('rating', 0)}")
            print(f"评论数: {product.get('review_count', 0)}")
            print(f"购买数: {product.get('bought_num', 0)}")
            print(f"图片: {product.get('image_url', 'N/A')[:50]}..." if product.get('image_url') else "图片: N/A")
            print(f"广告: {'是' if product.get('is_sponsored', 0) else '否'}")
        
        # 评估解析质量
        critical_fields = ['asin', 'title', 'product_url']
        critical_success = all(stats[field] / total > 0.8 for field in critical_fields)
        
        important_fields = ['price', 'rating', 'image_url']
        important_success = sum(stats[field] / total > 0.5 for field in important_fields) >= 2
        
        if critical_success and important_success:
            print(f"\n✅ {file_name} 解析质量: 优秀")
            quality = "excellent"
        elif critical_success:
            print(f"\n⚠️ {file_name} 解析质量: 良好")
            quality = "good"
        else:
            print(f"\n❌ {file_name} 解析质量: 需要改进")
            quality = "poor"
        
        return {
            'success': True,
            'products_count': len(products),
            'stats': stats,
            'quality': quality,
            'products': products[:3]  # 保存前3个商品用于对比
        }
        
    except Exception as e:
        print(f"❌ 解析 {file_name} 失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_parsing_results(result1, result2):
    """对比两个文件的解析结果"""
    print(f"\n📊 解析结果对比")
    print("=" * 60)
    
    if not result1 or not result2:
        print("❌ 无法对比，部分文件解析失败")
        return
    
    print(f"文件对比:")
    print(f"  list.html:  {result1['products_count']} 个商品, 质量: {result1['quality']}")
    print(f"  list2.html: {result2['products_count']} 个商品, 质量: {result2['quality']}")
    
    # 对比字段完整性
    print(f"\n字段完整性对比:")
    print(f"{'字段':<15} {'list.html':<12} {'list2.html':<12} {'差异':<10}")
    print("-" * 50)
    
    for field in result1['stats']:
        count1 = result1['stats'][field]
        count2 = result2['stats'][field]
        total1 = result1['products_count']
        total2 = result2['products_count']
        
        rate1 = (count1 / total1 * 100) if total1 > 0 else 0
        rate2 = (count2 / total2 * 100) if total2 > 0 else 0
        diff = rate2 - rate1
        
        diff_str = f"{diff:+.1f}%" if abs(diff) > 0.1 else "相同"
        
        print(f"{field:<15} {rate1:>6.1f}%     {rate2:>6.1f}%     {diff_str:<10}")
    
    # 检查一致性
    consistency_issues = []
    
    # 检查商品数量差异
    count_diff = abs(result1['products_count'] - result2['products_count'])
    if count_diff > result1['products_count'] * 0.1:  # 超过10%差异
        consistency_issues.append(f"商品数量差异较大: {count_diff}")
    
    # 检查关键字段差异
    for field in ['asin', 'title', 'product_url']:
        rate1 = result1['stats'][field] / result1['products_count'] * 100
        rate2 = result2['stats'][field] / result2['products_count'] * 100
        if abs(rate1 - rate2) > 20:  # 超过20%差异
            consistency_issues.append(f"{field}字段完整性差异: {abs(rate1-rate2):.1f}%")
    
    if consistency_issues:
        print(f"\n⚠️ 一致性问题:")
        for issue in consistency_issues:
            print(f"  - {issue}")
    else:
        print(f"\n✅ 两个文件的解析结果基本一致")

def test_specific_selectors():
    """测试特定选择器的效果"""
    print(f"\n🔧 测试特定选择器效果")
    print("=" * 60)
    
    # 测试文件
    test_files = [
        ("doc/list.html", "list.html"),
        ("doc/list2.html", "list2.html")
    ]
    
    for file_path, file_name in test_files:
        if not os.path.exists(file_path):
            print(f"⚠️ 文件不存在: {file_path}")
            continue
        
        html_content = load_html_file(file_path)
        if not html_content:
            continue
        
        soup = BeautifulSoup(html_content, 'html.parser')
        
        print(f"\n📋 {file_name} 选择器测试:")
        
        # 测试不同的商品容器选择器
        container_selectors = [
            '[data-component-type="s-search-result"]',
            'div[data-asin]',
            '.s-result-item[data-asin]',
            '[data-asin]:not([data-asin=""])'
        ]
        
        for selector in container_selectors:
            containers = soup.select(selector)
            print(f"  {selector}: {len(containers)} 个容器")
        
        # 如果找到容器，测试内部选择器
        containers = soup.select('[data-asin]:not([data-asin=""])')
        if containers:
            container = containers[0]
            
            print(f"  测试第一个容器的内部选择器:")
            
            # 测试标题选择器
            title_selectors = [
                'h2 a span[aria-label]',
                'h2 a span',
                '[data-cy="title-recipe"] span',
                'h2 span',
                '.a-size-base-plus',
                '.a-size-mini span'
            ]
            
            for selector in title_selectors:
                elem = container.select_one(selector)
                if elem:
                    text = elem.get_text(strip=True)[:30]
                    print(f"    标题 {selector}: ✅ '{text}...'")
                else:
                    print(f"    标题 {selector}: ❌")
            
            # 测试链接选择器
            link_selectors = [
                'h2 a[href*="/dp/"]',
                'a[href*="/dp/"]',
                'h2 a',
                '.a-link-normal[href*="/dp/"]'
            ]
            
            for selector in link_selectors:
                elem = container.select_one(selector)
                if elem and elem.get('href'):
                    href = elem['href'][:30]
                    print(f"    链接 {selector}: ✅ '{href}...'")
                else:
                    print(f"    链接 {selector}: ❌")
            
            # 测试Prime选择器
            prime_selectors = [
                'i[aria-label*="Prime"]',
                '[aria-label*="Prime"]',
                '.a-icon-prime',
                'span[aria-label*="Prime"]'
            ]
            
            for selector in prime_selectors:
                elem = container.select_one(selector)
                if elem:
                    text = elem.get('aria-label', '') or elem.get_text(strip=True)
                    print(f"    Prime {selector}: ✅ '{text[:20]}...'")
                else:
                    print(f"    Prime {selector}: ❌")

def main():
    """主函数"""
    print("🚀 Amazon HTML文件解析测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 检查文件是否存在
    files_to_test = [
        ("doc/list.html", "list.html"),
        ("doc/list2.html", "list2.html")
    ]
    
    existing_files = []
    for file_path, file_name in files_to_test:
        if os.path.exists(file_path):
            existing_files.append((file_path, file_name))
            print(f"✅ 找到文件: {file_path}")
        else:
            print(f"❌ 文件不存在: {file_path}")
    
    if len(existing_files) < 2:
        print(f"\n⚠️ 只找到 {len(existing_files)} 个文件，无法进行完整测试")
        if len(existing_files) == 0:
            return 1
    
    try:
        # 测试每个文件的解析
        results = []
        
        for file_path, file_name in existing_files:
            result = test_html_file_parsing(file_path, file_name)
            results.append(result)
        
        # 如果有两个文件，进行对比
        if len(results) == 2 and results[0] and results[1]:
            compare_parsing_results(results[0], results[1])
        
        # 测试特定选择器
        test_specific_selectors()
        
        # 总结
        print(f"\n✅ HTML解析测试完成!")
        print("=" * 50)
        
        success_count = sum(1 for r in results if r)
        print(f"📊 测试结果:")
        print(f"  成功解析: {success_count}/{len(existing_files)} 个文件")
        
        if success_count == len(existing_files):
            print(f"  🎉 所有文件解析成功!")
            
            # 给出改进建议
            print(f"\n💡 解析质量评估:")
            for i, (file_path, file_name) in enumerate(existing_files):
                if i < len(results) and results[i]:
                    quality = results[i]['quality']
                    if quality == 'excellent':
                        print(f"  {file_name}: ✅ 优秀")
                    elif quality == 'good':
                        print(f"  {file_name}: ⚠️ 良好 (可优化Prime信息等字段)")
                    else:
                        print(f"  {file_name}: ❌ 需要改进 (关键字段缺失)")
        else:
            print(f"  ⚠️ 部分文件解析失败，需要检查页面结构")
        
        return 0 if success_count > 0 else 1
        
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
