#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的数据提取功能
验证 URL 和 Prime 信息是否能正确提取
"""

import sys
import os
import time
import random
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test_crawler import AmazonCrawler
from crawler_config import get_config

def test_single_page_extraction():
    """测试单页数据提取"""
    print("🧪 测试改进后的数据提取功能")
    print("=" * 60)
    
    try:
        # 创建爬虫实例
        config = get_config()
        crawler = AmazonCrawler(config)
        
        # 测试URL
        test_url = "https://www.amazon.com/s?k=party+decorations&i=toys-and-games&rh=p_76%3A2661625011&page=1"
        
        print(f"📋 测试URL: {test_url}")
        
        # 发送请求
        print("📡 发送请求...")
        response = crawler.make_request(test_url)
        
        if response.status_code != 200:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return False
        
        print(f"✅ 请求成功，状态码: {response.status_code}")
        
        # 解析页面
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 提取商品
        print("🔍 提取商品数据...")
        products = crawler.extract_products_from_page(soup)
        
        if not products:
            print("❌ 没有提取到商品数据")
            return False
        
        print(f"✅ 提取到 {len(products)} 个商品")
        
        # 分析提取结果
        print("\n📊 数据提取分析:")
        
        url_count = sum(1 for p in products if p.get('product_url'))
        prime_count = sum(1 for p in products if p.get('prime_info'))
        title_count = sum(1 for p in products if p.get('title'))
        
        print(f"  有URL的商品: {url_count}/{len(products)} ({url_count/len(products)*100:.1f}%)")
        print(f"  有Prime信息的商品: {prime_count}/{len(products)} ({prime_count/len(products)*100:.1f}%)")
        print(f"  有标题的商品: {title_count}/{len(products)} ({title_count/len(products)*100:.1f}%)")
        
        # 显示前3个商品的详细信息
        print(f"\n📋 前3个商品详细信息:")
        
        for i, product in enumerate(products[:3], 1):
            print(f"\n--- 商品 {i} ---")
            print(f"ASIN: {product.get('asin', 'N/A')}")
            print(f"标题: {product.get('title', 'N/A')[:60]}...")
            print(f"URL: {product.get('product_url', 'N/A')[:80]}...")
            print(f"Prime: {product.get('prime_info', 'N/A')}")
            print(f"价格: ${product.get('price', 0)}")
            print(f"评分: {product.get('rating', 0)}")
            print(f"评论数: {product.get('review_count', 0)}")
        
        # 检查改进效果
        if url_count > len(products) * 0.8:  # 80%以上有URL
            print("\n✅ URL提取效果良好")
        else:
            print(f"\n⚠️ URL提取效果需要改进 ({url_count/len(products)*100:.1f}%)")
        
        if prime_count > len(products) * 0.3:  # 30%以上有Prime信息
            print("✅ Prime信息提取效果良好")
        else:
            print(f"⚠️ Prime信息提取效果需要改进 ({prime_count/len(products)*100:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_save():
    """测试数据库保存功能"""
    print("\n🧪 测试数据库保存功能")
    print("=" * 60)
    
    try:
        # 创建爬虫实例
        config = get_config()
        crawler = AmazonCrawler(config)
        
        # 模拟商品数据
        test_products = [
            {
                'asin': 'TEST001',
                'title': '测试商品1 - Party Decorations',
                'product_url': 'https://www.amazon.com/dp/TEST001',
                'prime_info': 'Prime delivery',
                'price': 19.99,
                'original_price': 24.99,
                'rating': 4.5,
                'review_count': 123,
                'image_url': 'https://example.com/image1.jpg',
                'is_sponsored': 0,
                'bought_num': 50
            },
            {
                'asin': 'TEST002',
                'title': '测试商品2 - Birthday Party Supplies',
                'product_url': 'https://www.amazon.com/dp/TEST002',
                'prime_info': 'FREE Prime delivery',
                'price': 15.99,
                'original_price': 0.00,
                'rating': 4.2,
                'review_count': 89,
                'image_url': 'https://example.com/image2.jpg',
                'is_sponsored': 1,
                'bought_num': 100
            }
        ]
        
        print(f"📋 准备保存 {len(test_products)} 个测试商品")
        
        # 保存到数据库
        task_id = 99999  # 测试任务ID
        amazon_category_id = 88888  # 测试类目ID
        
        result = crawler.save_products_to_db(test_products, task_id, amazon_category_id)
        
        print(f"✅ 保存结果: {result}")
        
        if result['inserted'] > 0:
            print("✅ 数据库保存功能正常")
            
            # 验证保存的数据
            import pymysql
            conn = pymysql.connect(**config['db'])
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            cursor.execute("""
                SELECT entry_asin, url, list_page_prime_info, list_task_id, amazon_category_id
                FROM zz_amazon_page_tasks
                WHERE entry_asin IN ('TEST001', 'TEST002')
                ORDER BY entry_asin
            """)
            
            saved_data = cursor.fetchall()
            
            print(f"\n📋 验证保存的数据:")
            for data in saved_data:
                print(f"  ASIN: {data['entry_asin']}")
                print(f"    URL: {data['url']}")
                print(f"    Prime: {data['list_page_prime_info']}")
                print(f"    Task ID: {data['list_task_id']}")
                print(f"    Category ID: {data['amazon_category_id']}")
                print()
            
            # 清理测试数据
            cursor.execute("DELETE FROM zz_amazon_page_tasks WHERE entry_asin IN ('TEST001', 'TEST002')")
            conn.commit()
            conn.close()
            
            print("🧹 测试数据已清理")
            
            return True
        else:
            print("❌ 数据库保存失败")
            return False
        
    except Exception as e:
        print(f"❌ 数据库保存测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_mini_crawl_test():
    """运行小规模爬取测试"""
    print("\n🧪 运行小规模爬取测试")
    print("=" * 60)
    
    try:
        # 创建爬虫实例
        config = get_config()
        crawler = AmazonCrawler(config)
        
        # 模拟一个简单的任务
        test_task = {
            'id': 99999,
            'url': 'https://www.amazon.com/s?k=party+hats&i=toys-and-games&rh=p_76%3A2661625011',
            'source_category_id': 88888,
            'max_pages_to_crawl': 1,
            'crawled_pages': 0,
            'status': 'pending'
        }
        
        print(f"📋 测试任务: {test_task['url']}")
        print(f"📋 最大页数: {test_task['max_pages_to_crawl']}")
        
        # 执行爬取
        print("🚀 开始爬取...")
        
        # 这里我们只测试第一页
        url = test_task['url']
        task_id = test_task['id']
        category_id = test_task['source_category_id']
        
        # 发送请求
        response = crawler.make_request(url, task_id=task_id)
        
        if response.status_code != 200:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return False
        
        # 解析页面
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 提取商品
        products = crawler.extract_products_from_page(soup)
        
        if not products:
            print("❌ 没有提取到商品")
            return False
        
        print(f"✅ 提取到 {len(products)} 个商品")
        
        # 保存商品（使用测试前缀避免污染数据）
        test_products = []
        for product in products[:3]:  # 只保存前3个
            test_product = product.copy()
            test_product['asin'] = f"TEST_{product['asin']}"  # 添加测试前缀
            test_products.append(test_product)
        
        if test_products:
            result = crawler.save_products_to_db(test_products, task_id, category_id)
            print(f"✅ 保存结果: {result}")
            
            # 验证保存的数据
            import pymysql
            conn = pymysql.connect(**config['db'])
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            cursor.execute("""
                SELECT entry_asin, url, list_page_prime_info, list_task_id, amazon_category_id
                FROM zz_amazon_page_tasks
                WHERE entry_asin LIKE 'TEST_%'
                ORDER BY created_at DESC
                LIMIT 3
            """)
            
            saved_data = cursor.fetchall()
            
            print(f"\n📋 保存的测试数据:")
            for data in saved_data:
                url_status = "✅" if data['url'] else "❌"
                prime_status = "✅" if data['list_page_prime_info'] else "❌"
                
                print(f"  ASIN: {data['entry_asin']}")
                print(f"    URL: {url_status} {data['url'][:50]}..." if data['url'] else f"    URL: {url_status} (空)")
                print(f"    Prime: {prime_status} {data['list_page_prime_info']}" if data['list_page_prime_info'] else f"    Prime: {prime_status} (空)")
                print()
            
            # 清理测试数据
            cursor.execute("DELETE FROM zz_amazon_page_tasks WHERE entry_asin LIKE 'TEST_%'")
            conn.commit()
            conn.close()
            
            print("🧹 测试数据已清理")
        
        return True
        
    except Exception as e:
        print(f"❌ 小规模爬取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 Amazon数据提取改进测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # 1. 测试单页数据提取
        extraction_success = test_single_page_extraction()
        
        # 2. 测试数据库保存
        save_success = test_database_save()
        
        # 3. 运行小规模爬取测试
        crawl_success = run_mini_crawl_test()
        
        # 总结
        print("\n✅ 测试完成!")
        print("=" * 50)
        print("📊 测试结果:")
        print(f"  数据提取: {'✅ 通过' if extraction_success else '❌ 失败'}")
        print(f"  数据库保存: {'✅ 通过' if save_success else '❌ 失败'}")
        print(f"  爬取测试: {'✅ 通过' if crawl_success else '❌ 失败'}")
        
        if extraction_success and save_success and crawl_success:
            print("\n🎉 所有测试通过！改进的数据提取功能正常工作")
            print("\n📋 改进效果:")
            print("- URL提取使用多个备用选择器")
            print("- Prime信息提取增强，支持文本模式匹配")
            print("- 标题提取更加可靠")
            print("- 数据库保存包含所有必要字段")
        else:
            print("\n⚠️ 部分测试失败，需要进一步调试")
        
        return 0 if (extraction_success and save_success and crawl_success) else 1
        
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
