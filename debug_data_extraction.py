#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据提取问题
检查 url 和 list_page_prime_info 字段为空的原因
"""

import requests
import pymysql
from bs4 import BeautifulSoup
from decimal import Decimal
import re
import sys
from datetime import datetime
from crawler_config import get_config
from user_agents import get_headers

def test_amazon_page_extraction():
    """测试Amazon页面数据提取"""
    print("🔍 测试Amazon页面数据提取")
    print("=" * 60)
    
    # 测试URL
    test_url = "https://www.amazon.com/s?k=Kids+Party+Centerpieces&i=toys-and-games&rh=n%3A2528084011%2Cp_76%3A2661625011%2Cp_36%3A-1000"
    
    try:
        # 发送请求
        headers = get_headers()
        print(f"📋 请求URL: {test_url}")
        print(f"📋 User-Agent: {headers.get('User-Agent', 'None')}")
        
        response = requests.get(test_url, headers=headers, timeout=30)
        response.raise_for_status()
        
        print(f"✅ 请求成功，状态码: {response.status_code}")
        print(f"📊 响应大小: {len(response.text)} 字符")
        
        # 解析页面
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找商品容器
        print("\n🔍 查找商品容器...")
        
        # 尝试不同的选择器
        selectors = [
            '[data-component-type="s-search-result"]',
            'div[data-asin]',
            '.s-result-item[data-asin]',
            '[data-asin]:not([data-asin=""])'
        ]
        
        products_found = []
        
        for selector in selectors:
            containers = soup.select(selector)
            print(f"  选择器 '{selector}': 找到 {len(containers)} 个容器")
            
            if containers:
                products_found = containers
                break
        
        if not products_found:
            print("❌ 没有找到商品容器")
            # 保存页面内容用于调试
            with open('debug_amazon_page.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print("📄 页面内容已保存到 debug_amazon_page.html")
            return False
        
        print(f"✅ 找到 {len(products_found)} 个商品容器")
        
        # 测试前3个商品的数据提取
        print("\n📦 测试商品数据提取:")
        
        for i, container in enumerate(products_found[:3], 1):
            print(f"\n--- 商品 {i} ---")
            
            # 提取ASIN
            asin = container.get('data-asin', '')
            print(f"ASIN: {asin}")
            
            # 提取标题
            title_selectors = [
                'h2 a span',
                '[data-cy="title-recipe"] h2 span',
                'h2 span',
                '.a-size-mini span',
                '.a-size-base-plus'
            ]
            
            title = ''
            for selector in title_selectors:
                title_elem = container.select_one(selector)
                if title_elem:
                    title = title_elem.get_text(strip=True)
                    print(f"标题 (选择器: {selector}): {title[:50]}...")
                    break
            
            if not title:
                print("❌ 未找到标题")
            
            # 提取商品链接
            link_selectors = [
                'h2 a',
                'a[href*="/dp/"]',
                'a[href*="/gp/product/"]'
            ]
            
            product_url = ''
            for selector in link_selectors:
                link_elem = container.select_one(selector)
                if link_elem and link_elem.get('href'):
                    href = link_elem['href']
                    if href.startswith('/'):
                        product_url = f"https://www.amazon.com{href}"
                    else:
                        product_url = href
                    print(f"链接 (选择器: {selector}): {product_url[:80]}...")
                    break
            
            if not product_url:
                print("❌ 未找到商品链接")
                # 查看所有链接
                all_links = container.select('a[href]')
                print(f"  容器内所有链接数: {len(all_links)}")
                for j, link in enumerate(all_links[:3]):
                    href = link.get('href', '')
                    print(f"    链接{j+1}: {href[:60]}...")
            
            # 提取Prime信息
            prime_selectors = [
                '[aria-label*="Prime"]',
                '.a-icon-prime',
                '[data-testid*="prime"]',
                '.s-prime',
                'i[aria-label*="Prime"]'
            ]
            
            prime_info = ''
            for selector in prime_selectors:
                prime_elem = container.select_one(selector)
                if prime_elem:
                    prime_text = prime_elem.get('aria-label', '') or prime_elem.get_text(strip=True)
                    if prime_text:
                        prime_info = prime_text[:255]
                        print(f"Prime信息 (选择器: {selector}): {prime_info}")
                        break
            
            if not prime_info:
                print("❌ 未找到Prime信息")
                # 查看是否有Prime相关的文本
                container_text = container.get_text()
                if 'prime' in container_text.lower():
                    print("  ⚠️ 容器中包含'prime'文本，但选择器未匹配")
            
            # 提取价格
            price_selectors = [
                '.a-price .a-offscreen',
                '.a-price-whole',
                '.a-price .a-price-whole'
            ]
            
            price = Decimal('0.00')
            for selector in price_selectors:
                price_elem = container.select_one(selector)
                if price_elem:
                    price_text = price_elem.get_text(strip=True)
                    price_match = re.search(r'[\d,]+\.?\d*', price_text.replace(',', '').replace('$', ''))
                    if price_match:
                        try:
                            price = Decimal(price_match.group())
                            print(f"价格 (选择器: {selector}): ${price}")
                            break
                        except:
                            pass
            
            if price == Decimal('0.00'):
                print("❌ 未找到价格")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_database_data():
    """检查数据库中的数据情况"""
    print("\n🔍 检查数据库中的数据情况")
    print("=" * 60)
    
    try:
        config = get_config()
        conn = pymysql.connect(**config['db'])
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # 检查url字段为空的记录
        cursor.execute("""
            SELECT COUNT(*) as total,
                   COUNT(CASE WHEN url IS NULL OR url = '' THEN 1 END) as empty_url,
                   COUNT(CASE WHEN list_page_prime_info IS NULL OR list_page_prime_info = '' THEN 1 END) as empty_prime
            FROM zz_amazon_page_tasks
        """)
        
        stats = cursor.fetchone()
        
        print(f"📊 数据统计:")
        print(f"  总记录数: {stats['total']}")
        print(f"  URL为空: {stats['empty_url']}")
        print(f"  Prime信息为空: {stats['empty_prime']}")
        
        if stats['total'] > 0:
            url_empty_rate = (stats['empty_url'] / stats['total']) * 100
            prime_empty_rate = (stats['empty_prime'] / stats['total']) * 100
            print(f"  URL空值率: {url_empty_rate:.1f}%")
            print(f"  Prime空值率: {prime_empty_rate:.1f}%")
        
        # 显示一些示例记录
        cursor.execute("""
            SELECT entry_asin, url, list_page_title, list_page_prime_info, created_at
            FROM zz_amazon_page_tasks
            ORDER BY created_at DESC
            LIMIT 5
        """)
        
        samples = cursor.fetchall()
        
        print(f"\n📋 最新5条记录示例:")
        for sample in samples:
            print(f"  ASIN: {sample['entry_asin']}")
            print(f"    标题: {sample['list_page_title'][:50]}...")
            print(f"    URL: {sample['url'][:60]}..." if sample['url'] else "    URL: (空)")
            print(f"    Prime: {sample['list_page_prime_info']}" if sample['list_page_prime_info'] else "    Prime: (空)")
            print(f"    时间: {sample['created_at']}")
            print()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")

def test_improved_selectors():
    """测试改进的选择器"""
    print("\n🔧 测试改进的选择器")
    print("=" * 60)
    
    # 改进的选择器配置
    improved_selectors = {
        'product_link': [
            'h2 a[href*="/dp/"]',
            'a[href*="/dp/"]',
            'h2 a',
            '.a-link-normal[href*="/dp/"]',
            'a[href*="/gp/product/"]'
        ],
        'prime_info': [
            'i[aria-label*="Prime"]',
            '[aria-label*="Prime"]',
            '.a-icon-prime',
            'span[aria-label*="Prime"]',
            '.s-prime',
            '[data-testid*="prime"]',
            'i.a-icon.a-icon-prime'
        ],
        'title': [
            'h2 a span[aria-label]',
            'h2 a span',
            '[data-cy="title-recipe"] span',
            'h2 span',
            '.a-size-base-plus',
            '.a-size-mini span'
        ]
    }
    
    print("📋 改进的选择器配置:")
    for field, selectors in improved_selectors.items():
        print(f"  {field}:")
        for selector in selectors:
            print(f"    - {selector}")
    
    return improved_selectors

def create_improved_extraction_function():
    """创建改进的数据提取函数"""
    print("\n🔧 创建改进的数据提取函数")
    print("=" * 60)
    
    improved_code = '''
def extract_single_product_improved(self, container):
    """改进的单个商品数据提取"""
    try:
        product_data = {}
        
        # 提取ASIN
        asin = container.get('data-asin', '')
        if not asin:
            return None
        product_data['asin'] = asin
        
        # 提取标题 - 使用多个选择器
        title_selectors = [
            'h2 a span[aria-label]',
            'h2 a span',
            '[data-cy="title-recipe"] span',
            'h2 span',
            '.a-size-base-plus',
            '.a-size-mini span'
        ]
        
        title = ''
        for selector in title_selectors:
            title_elem = container.select_one(selector)
            if title_elem:
                title = title_elem.get_text(strip=True)
                if title:  # 确保不是空字符串
                    break
        product_data['title'] = title
        
        # 提取商品链接 - 使用多个选择器
        link_selectors = [
            'h2 a[href*="/dp/"]',
            'a[href*="/dp/"]',
            'h2 a',
            '.a-link-normal[href*="/dp/"]',
            'a[href*="/gp/product/"]'
        ]
        
        product_url = ''
        for selector in link_selectors:
            link_elem = container.select_one(selector)
            if link_elem and link_elem.get('href'):
                href = link_elem['href']
                if href.startswith('/'):
                    product_url = f"https://www.amazon.com{href}"
                else:
                    product_url = href
                if product_url:  # 确保不是空字符串
                    break
        product_data['product_url'] = product_url
        
        # 提取Prime信息 - 使用多个选择器
        prime_selectors = [
            'i[aria-label*="Prime"]',
            '[aria-label*="Prime"]',
            '.a-icon-prime',
            'span[aria-label*="Prime"]',
            '.s-prime',
            '[data-testid*="prime"]',
            'i.a-icon.a-icon-prime'
        ]
        
        prime_info = ''
        for selector in prime_selectors:
            prime_elem = container.select_one(selector)
            if prime_elem:
                prime_text = prime_elem.get('aria-label', '') or prime_elem.get_text(strip=True)
                if prime_text and 'prime' in prime_text.lower():
                    prime_info = prime_text[:255]
                    break
        
        # 如果没找到Prime元素，检查文本内容
        if not prime_info:
            container_text = container.get_text()
            if 'prime' in container_text.lower():
                # 尝试提取Prime相关文本
                prime_patterns = [
                    r'(Prime\\s+\\w+)',
                    r'(FREE\\s+.*?Prime)',
                    r'(Prime\\s+delivery)'
                ]
                for pattern in prime_patterns:
                    match = re.search(pattern, container_text, re.IGNORECASE)
                    if match:
                        prime_info = match.group(1)[:255]
                        break
        
        product_data['prime_info'] = prime_info
        
        # ... 其他字段提取保持不变 ...
        
        return product_data
        
    except Exception as e:
        self.logger.error(f"提取商品数据失败: {e}")
        return None
'''
    
    print("📄 改进的提取函数已生成")
    
    # 保存到文件
    with open('improved_extraction.py', 'w', encoding='utf-8') as f:
        f.write(improved_code)
    
    print("💾 改进的代码已保存到 improved_extraction.py")

def main():
    """主函数"""
    print("🚀 Amazon数据提取调试工具")
    print("=" * 60)
    print(f"调试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # 1. 检查数据库数据情况
        check_database_data()
        
        # 2. 测试Amazon页面数据提取
        extraction_success = test_amazon_page_extraction()
        
        # 3. 测试改进的选择器
        improved_selectors = test_improved_selectors()
        
        # 4. 创建改进的提取函数
        create_improved_extraction_function()
        
        print("\n✅ 调试完成!")
        print("=" * 50)
        print("📋 发现的问题:")
        
        if not extraction_success:
            print("❌ Amazon页面数据提取失败")
            print("   可能原因: 选择器不匹配当前页面结构")
        
        print("\n💡 解决方案:")
        print("1. 使用改进的多选择器策略")
        print("2. 增加更多的备用选择器")
        print("3. 添加文本模式匹配作为备用方案")
        print("4. 更新 test_crawler.py 中的提取函数")
        
        print("\n📋 下一步操作:")
        print("1. 查看 debug_amazon_page.html (如果生成)")
        print("2. 查看 improved_extraction.py 中的改进代码")
        print("3. 更新 test_crawler.py 中的 extract_single_product 方法")
        
        return 0
        
    except Exception as e:
        print(f"❌ 调试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
