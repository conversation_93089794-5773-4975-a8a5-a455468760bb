#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Amazon多线程爬虫系统
从zz_amazon_list_tasks获取任务，支持多线程、代理轮换、用户代理轮换
"""

import os
import sys
import time
import random
import threading
import queue
import signal
import pymysql
import requests
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging
from logging.handlers import RotatingFileHandler
from bs4 import BeautifulSoup
from decimal import Decimal
import re
from urllib.parse import urljoin, urlparse, parse_qs, urlencode, urlunparse

# 导入自定义模块
from user_agents import get_headers, get_random_user_agent
from proxy_manager import FyndiqProxyManager

# 配置日志
def setup_logging():
    """设置日志配置"""
    # 创建logs目录
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # 创建logger
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    
    # 清除现有的handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 文件日志处理器 - 主日志
    file_handler = RotatingFileHandler(
        'logs/amazon_crawler.log',
        maxBytes=50*1024*1024,  # 50MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)
    
    # 错误日志处理器
    error_handler = RotatingFileHandler(
        'logs/amazon_crawler_error.log',
        maxBytes=10*1024*1024,  # 10MB
        backupCount=3,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    
    # 控制台日志处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - [%(threadName)s] - %(message)s'
    )
    file_handler.setFormatter(formatter)
    error_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(error_handler)
    logger.addHandler(console_handler)
    
    return logger

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'database': 'xace200_lsh',
    'charset': 'utf8mb4',
    'autocommit': True
}

# 爬虫配置
CRAWLER_CONFIG = {
    'max_workers': 5,           # 最大线程数
    'request_timeout': 30,      # 请求超时时间
    'retry_count': 3,           # 重试次数
    'delay_range': (2, 5),      # 请求间隔范围（秒）
    'page_delay_range': (3, 8), # 页面间隔范围（秒）
    'use_proxy': True,          # 是否使用代理
    'rotate_user_agent': True,  # 是否轮换用户代理
}

class AmazonCrawler:
    """Amazon爬虫类"""
    
    def __init__(self, config=None):
        """初始化爬虫"""
        self.config = config or CRAWLER_CONFIG
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化代理管理器
        if self.config.get('use_proxy'):
            self.proxy_manager = FyndiqProxyManager(DB_CONFIG)
            self.proxy_manager.load_proxies()
            self.logger.info("代理管理器初始化完成")
        else:
            self.proxy_manager = None
        
        # 线程控制
        self.running = True
        self.task_queue = queue.Queue()
        self.result_queue = queue.Queue()
        
        # 统计信息
        self.stats = {
            'tasks_processed': 0,
            'tasks_success': 0,
            'tasks_failed': 0,
            'pages_crawled': 0,
            'products_found': 0,
            'start_time': None,
            'errors': []
        }
        
        # 线程锁
        self.stats_lock = threading.Lock()
    
    def get_db_connection(self):
        """获取数据库连接"""
        return pymysql.connect(**DB_CONFIG)
    
    def load_pending_tasks(self):
        """从数据库加载待处理任务"""
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            # 查询待处理的任务
            query = """
                SELECT id, url, source_category_id, max_pages_to_crawl, crawled_pages, status
                FROM zz_amazon_list_tasks
                WHERE status IN ('pending', 'in_progress')
                ORDER BY created_at ASC
                LIMIT 100
            """
            cursor.execute(query)
            tasks = cursor.fetchall()
            
            self.logger.info(f"从数据库加载了 {len(tasks)} 个待处理任务")
            
            # 将任务添加到队列
            for task in tasks:
                self.task_queue.put(task)
            
            return len(tasks)
            
        except Exception as e:
            self.logger.error(f"加载任务失败: {e}")
            return 0
        finally:
            if 'conn' in locals():
                conn.close()
    
    def update_task_status(self, task_id, status, crawled_pages=None, error_message=None):
        """更新任务状态"""
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()
            
            update_fields = ["status = %s", "updated_at = NOW()"]
            update_values = [status]
            
            if crawled_pages is not None:
                update_fields.append("crawled_pages = %s")
                update_values.append(crawled_pages)
            
            if error_message is not None:
                update_fields.append("error_message = %s")
                update_values.append(error_message)
            
            update_values.append(task_id)
            
            query = f"UPDATE zz_amazon_list_tasks SET {', '.join(update_fields)} WHERE id = %s"
            cursor.execute(query, update_values)
            
            self.logger.debug(f"更新任务 {task_id} 状态: {status}")
            
        except Exception as e:
            self.logger.error(f"更新任务状态失败: {e}")
        finally:
            if 'conn' in locals():
                conn.close()
    
    def make_request(self, url, **kwargs):
        """发送HTTP请求"""
        # 获取请求头
        if self.config.get('rotate_user_agent'):
            headers = get_headers()
        else:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }
        
        # 合并自定义headers
        if 'headers' in kwargs:
            headers.update(kwargs['headers'])
        kwargs['headers'] = headers
        
        # 获取代理
        proxies = None
        proxy_url = None
        if self.proxy_manager:
            proxy_dict = self.proxy_manager.get_random_proxy()
            if proxy_dict:
                proxies = {
                    'http': proxy_dict['http'],
                    'https': proxy_dict['https']
                }
                proxy_url = proxy_dict['http']
                kwargs['timeout'] = proxy_dict.get('timeout', self.config['request_timeout'])
        
        if not kwargs.get('timeout'):
            kwargs['timeout'] = self.config['request_timeout']
        
        # 发送请求
        start_time = time.time()
        try:
            if proxies:
                kwargs['proxies'] = proxies
            
            response = requests.get(url, **kwargs)
            response_time = time.time() - start_time
            
            # 记录代理成功
            if proxy_url and self.proxy_manager:
                self.proxy_manager.record_proxy_success(proxy_url)
            
            self.logger.debug(f"请求成功: {url} (耗时: {response_time:.2f}s)")
            return response
            
        except Exception as e:
            # 记录代理失败
            if proxy_url and self.proxy_manager:
                self.proxy_manager.record_proxy_failure(proxy_url)
            
            self.logger.error(f"请求失败: {url} - {e}")
            raise
    
    def extract_products_from_page(self, soup):
        """从页面提取商品数据"""
        products = []
        
        try:
            # 查找商品容器
            product_containers = soup.select('[data-component-type="s-search-result"]')
            
            for container in product_containers:
                try:
                    product_data = self.extract_single_product(container)
                    if product_data:
                        products.append(product_data)
                except Exception as e:
                    self.logger.error(f"提取单个商品失败: {e}")
                    continue
            
            self.logger.debug(f"从页面提取了 {len(products)} 个商品")
            
        except Exception as e:
            self.logger.error(f"提取商品数据失败: {e}")
        
        return products
    
    def extract_single_product(self, container):
        """提取单个商品数据"""
        try:
            product_data = {}

            # 提取ASIN
            asin = container.get('data-asin', '')
            if not asin:
                return None
            product_data['asin'] = asin

            # 提取标题
            title_elem = container.select_one('h2 a span, [data-cy="title-recipe"] h2 span')
            product_data['title'] = title_elem.get_text(strip=True) if title_elem else ''

            # 提取当前价格
            price_elem = container.select_one('.a-price .a-offscreen')
            current_price = Decimal('0.00')
            if price_elem:
                price_text = price_elem.get_text(strip=True)
                price_match = re.search(r'[\d,]+\.?\d*', price_text.replace(',', '').replace('$', ''))
                if price_match:
                    try:
                        current_price = Decimal(price_match.group())
                    except:
                        pass
            product_data['price'] = current_price

            # 提取原价
            original_price = Decimal('0.00')
            container_text = container.get_text()

            # 查找各种原价格式
            original_price_patterns = [
                r'List:\s*\$?([\d,]+\.?\d*)',
                r'Typical.*?:\s*\$?([\d,]+\.?\d*)',
                r'Was:\s*\$?([\d,]+\.?\d*)'
            ]

            for pattern in original_price_patterns:
                match = re.search(pattern, container_text, re.IGNORECASE)
                if match:
                    try:
                        original_price = Decimal(match.group(1).replace(',', ''))
                        break
                    except:
                        continue

            product_data['original_price'] = original_price

            # 提取评分
            rating_elem = container.select_one('.a-icon-alt')
            rating = Decimal('0.0')
            if rating_elem:
                rating_text = rating_elem.get_text(strip=True)
                rating_match = re.search(r'(\d+\.?\d*)', rating_text)
                if rating_match:
                    try:
                        rating = Decimal(rating_match.group(1))
                    except:
                        pass
            product_data['rating'] = rating

            # 提取评论数
            review_count = 0
            review_elem = container.select_one('a[aria-label*="ratings"]')
            if review_elem:
                review_text = review_elem.get('aria-label', '')
                review_match = re.search(r'(\d+)', review_text)
                if review_match:
                    try:
                        review_count = int(review_match.group(1))
                    except:
                        pass
            product_data['review_count'] = review_count

            # 提取购买数量
            bought_num = 0
            bought_patterns = [
                r'(\d+)\+?\s*bought\s*in\s*past\s*month',
                r'(\d+)K\+?\s*bought\s*in\s*past\s*month'
            ]

            for pattern in bought_patterns:
                bought_match = re.search(pattern, container_text, re.IGNORECASE)
                if bought_match:
                    try:
                        bought_str = bought_match.group(1)
                        if 'K' in pattern:
                            bought_num = int(float(bought_str) * 1000)
                        else:
                            bought_num = int(bought_str)
                        break
                    except:
                        continue

            product_data['bought_num'] = bought_num

            # 提取图片URL
            img_elem = container.select_one('img.s-image')
            product_data['image_url'] = img_elem.get('src', '') if img_elem else ''

            # 提取商品链接
            link_elem = container.select_one('h2 a')
            if link_elem and link_elem.get('href'):
                href = link_elem['href']
                if href.startswith('/'):
                    product_data['product_url'] = f"https://www.amazon.com{href}"
                else:
                    product_data['product_url'] = href
            else:
                product_data['product_url'] = ''

            # 检测Prime信息
            prime_elem = container.select_one('[aria-label*="Prime"], .a-icon-prime')
            prime_info = ''
            if prime_elem:
                prime_text = prime_elem.get('aria-label', '') or prime_elem.get_text(strip=True)
                if prime_text:
                    prime_info = prime_text[:255]
                else:
                    prime_info = 'Prime'
            product_data['prime_info'] = prime_info

            # 检测广告商品
            is_sponsored = 1 if 'sponsored' in container_text.lower() else 0
            product_data['is_sponsored'] = is_sponsored

            return product_data

        except Exception as e:
            self.logger.error(f"提取商品数据失败: {e}")
            return None

    def save_products_to_db(self, products, category_id):
        """保存商品到数据库"""
        if not products:
            return {'total': 0, 'inserted': 0, 'skipped': 0, 'failed': 0}

        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()

            results = {'total': len(products), 'inserted': 0, 'skipped': 0, 'failed': 0}

            for product in products:
                try:
                    asin = product.get('asin', '')
                    if not asin:
                        results['failed'] += 1
                        continue

                    # 检查是否已存在
                    cursor.execute("SELECT id FROM zz_amazon_page_tasks WHERE entry_asin = %s", (asin,))
                    if cursor.fetchone():
                        results['skipped'] += 1
                        continue

                    # 插入新记录
                    cursor.execute("""
                        INSERT INTO zz_amazon_page_tasks
                        (entry_asin, url, list_page_title, list_page_price, list_orgin_price,
                         list_page_rating, list_page_review_count, list_page_main_image_url,
                         list_page_prime_info, is_sponsored, list_bought_num, status)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 'pending')
                    """, (
                        asin,
                        product.get('product_url', ''),
                        product.get('title', '')[:512],
                        product.get('price', 0.00),
                        product.get('original_price', 0.00),
                        product.get('rating', 0.00),
                        product.get('review_count', 0),
                        product.get('image_url', ''),
                        product.get('prime_info', ''),
                        product.get('is_sponsored', 0),
                        product.get('bought_num', 0)
                    ))

                    results['inserted'] += 1

                except Exception as e:
                    self.logger.error(f"保存商品 {product.get('asin', 'unknown')} 失败: {e}")
                    results['failed'] += 1
                    continue

            self.logger.info(f"商品保存结果: 总计 {results['total']}, 新增 {results['inserted']}, 跳过 {results['skipped']}, 失败 {results['failed']}")
            return results

        except Exception as e:
            self.logger.error(f"保存商品数据失败: {e}")
            return {'total': len(products), 'inserted': 0, 'skipped': 0, 'failed': len(products)}
        finally:
            if 'conn' in locals():
                conn.close()

    def get_next_page_url(self, soup, current_url):
        """获取下一页URL"""
        try:
            # 查找下一页链接
            next_link = soup.select_one('a.s-pagination-next')

            if next_link and next_link.get('href'):
                next_url = next_link['href']
                if next_url.startswith('/'):
                    next_url = urljoin('https://www.amazon.com', next_url)
                return next_url

            # 备用方案：通过修改page参数
            parsed = urlparse(current_url)
            params = parse_qs(parsed.query)
            current_page = int(params.get('page', ['1'])[0])
            next_page = current_page + 1

            params['page'] = [str(next_page)]
            new_query = urlencode(params, doseq=True)
            next_url = urlunparse((
                parsed.scheme, parsed.netloc, parsed.path,
                parsed.params, new_query, parsed.fragment
            ))

            return next_url

        except Exception as e:
            self.logger.error(f"获取下一页URL失败: {e}")
            return None

    def crawl_single_task(self, task):
        """爬取单个任务"""
        task_id = task['id']
        url = task['url']
        category_id = task['source_category_id']
        max_pages = task['max_pages_to_crawl']
        crawled_pages = task['crawled_pages']

        self.logger.info(f"开始处理任务 {task_id}: {url}")

        try:
            # 更新任务状态为进行中
            self.update_task_status(task_id, 'in_progress')

            current_url = url
            current_page = crawled_pages + 1
            total_products = 0

            # 如果不是从第一页开始，构建当前页URL
            if current_page > 1:
                parsed = urlparse(url)
                params = parse_qs(parsed.query)
                params['page'] = [str(current_page)]
                new_query = urlencode(params, doseq=True)
                current_url = urlunparse((
                    parsed.scheme, parsed.netloc, parsed.path,
                    parsed.params, new_query, parsed.fragment
                ))

            while current_page <= max_pages and self.running:
                self.logger.info(f"任务 {task_id} - 正在抓取第 {current_page} 页")

                try:
                    # 发送请求
                    response = self.make_request(current_url)
                    response.raise_for_status()

                    # 解析页面
                    soup = BeautifulSoup(response.text, 'html.parser')

                    # 检查是否被反爬虫拦截
                    if "Robot Check" in response.text or "captcha" in response.text.lower():
                        self.logger.warning(f"任务 {task_id} - 检测到反爬虫验证")
                        time.sleep(random.uniform(10, 20))
                        continue

                    # 提取商品数据
                    products = self.extract_products_from_page(soup)

                    if products:
                        # 保存商品数据
                        save_results = self.save_products_to_db(products, category_id)
                        total_products += save_results['inserted']

                        # 更新统计
                        with self.stats_lock:
                            self.stats['products_found'] += len(products)

                    # 更新已抓取页数
                    self.update_task_status(task_id, 'in_progress', crawled_pages=current_page)

                    # 更新统计
                    with self.stats_lock:
                        self.stats['pages_crawled'] += 1

                    # 检查是否有下一页
                    if current_page >= max_pages:
                        break

                    next_url = self.get_next_page_url(soup, current_url)
                    if not next_url:
                        self.logger.info(f"任务 {task_id} - 没有更多页面")
                        break

                    current_url = next_url
                    current_page += 1

                    # 页面间延迟
                    delay = random.uniform(*self.config['page_delay_range'])
                    time.sleep(delay)

                except Exception as e:
                    self.logger.error(f"任务 {task_id} - 抓取第 {current_page} 页失败: {e}")
                    # 继续下一页
                    current_page += 1
                    continue

            # 标记任务完成
            self.update_task_status(task_id, 'completed')

            with self.stats_lock:
                self.stats['tasks_success'] += 1

            self.logger.info(f"任务 {task_id} 完成，共抓取 {current_page-1} 页，{total_products} 个新商品")

        except Exception as e:
            error_msg = f"任务执行失败: {e}"
            self.logger.error(f"任务 {task_id} - {error_msg}")
            self.update_task_status(task_id, 'failed', error_message=error_msg)

            with self.stats_lock:
                self.stats['tasks_failed'] += 1
                self.stats['errors'].append(f"任务 {task_id}: {error_msg}")

        finally:
            with self.stats_lock:
                self.stats['tasks_processed'] += 1

    def worker_thread(self, thread_id):
        """工作线程"""
        self.logger.info(f"工作线程 {thread_id} 启动")

        while self.running:
            try:
                # 从队列获取任务
                task = self.task_queue.get(timeout=5)

                if task is None:  # 停止信号
                    break

                # 处理任务
                self.crawl_single_task(task)

                # 任务间延迟
                delay = random.uniform(*self.config['delay_range'])
                time.sleep(delay)

                # 标记任务完成
                self.task_queue.task_done()

            except queue.Empty:
                # 队列为空，继续等待
                continue
            except Exception as e:
                self.logger.error(f"工作线程 {thread_id} 出错: {e}")
                continue

        self.logger.info(f"工作线程 {thread_id} 结束")

    def start_crawling(self):
        """启动爬虫"""
        self.logger.info("启动Amazon爬虫系统")

        # 初始化统计
        self.stats['start_time'] = datetime.now()

        # 加载待处理任务
        task_count = self.load_pending_tasks()
        if task_count == 0:
            self.logger.info("没有待处理的任务")
            return

        # 启动工作线程
        threads = []
        max_workers = min(self.config['max_workers'], task_count)

        for i in range(max_workers):
            thread = threading.Thread(
                target=self.worker_thread,
                args=(i+1,),
                name=f"Worker-{i+1}"
            )
            thread.daemon = True
            thread.start()
            threads.append(thread)

        self.logger.info(f"启动了 {max_workers} 个工作线程")

        try:
            # 等待所有任务完成
            self.task_queue.join()

        except KeyboardInterrupt:
            self.logger.info("收到中断信号，正在停止爬虫...")
            self.stop_crawling()

        # 停止所有线程
        self.running = False

        # 向队列添加停止信号
        for _ in range(max_workers):
            self.task_queue.put(None)

        # 等待线程结束
        for thread in threads:
            thread.join(timeout=10)

        # 打印统计信息
        self.print_stats()

        self.logger.info("Amazon爬虫系统已停止")

    def stop_crawling(self):
        """停止爬虫"""
        self.running = False
        self.logger.info("正在停止爬虫...")

    def print_stats(self):
        """打印统计信息"""
        with self.stats_lock:
            stats = self.stats.copy()

        if stats['start_time']:
            duration = datetime.now() - stats['start_time']
            duration_str = str(duration).split('.')[0]  # 去掉微秒
        else:
            duration_str = "未知"

        self.logger.info("=" * 60)
        self.logger.info("爬虫统计信息")
        self.logger.info("=" * 60)
        self.logger.info(f"运行时间: {duration_str}")
        self.logger.info(f"处理任务数: {stats['tasks_processed']}")
        self.logger.info(f"成功任务数: {stats['tasks_success']}")
        self.logger.info(f"失败任务数: {stats['tasks_failed']}")
        self.logger.info(f"抓取页面数: {stats['pages_crawled']}")
        self.logger.info(f"发现商品数: {stats['products_found']}")

        if stats['tasks_processed'] > 0:
            success_rate = (stats['tasks_success'] / stats['tasks_processed']) * 100
            self.logger.info(f"成功率: {success_rate:.1f}%")

        if stats['errors']:
            self.logger.info(f"错误数量: {len(stats['errors'])}")
            self.logger.info("最近的错误:")
            for error in stats['errors'][-5:]:  # 显示最近5个错误
                self.logger.info(f"  - {error}")

        self.logger.info("=" * 60)

def signal_handler(signum, frame):
    """信号处理器"""
    logger = logging.getLogger(__name__)
    logger.info(f"收到信号 {signum}，正在优雅关闭...")
    global crawler
    if crawler:
        crawler.stop_crawling()

def main():
    """主函数"""
    # 设置日志
    logger = setup_logging()

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    logger.info("Amazon多线程爬虫系统启动")
    logger.info(f"配置信息: {CRAWLER_CONFIG}")

    try:
        # 创建爬虫实例
        global crawler
        crawler = AmazonCrawler(CRAWLER_CONFIG)

        # 启动爬虫
        crawler.start_crawling()

    except Exception as e:
        logger.error(f"爬虫系统出错: {e}")
        import traceback
        logger.error(traceback.format_exc())

    logger.info("程序结束")

if __name__ == "__main__":
    main()
