import requests
from bs4 import BeautifulSoup
import re
import json
import time
import random
import pymysql
from pymysql.cursors import DictCursor
from urllib.parse import urljoin, urlparse

# --- 1. 配置信息 ---
DB_CONFIG = {
    'host': '**************',
    'user': 'root',
    'password': 'xh884813@@@XH',
    'database': 'xace200_lsh',
    'port': 22888
}

HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Accept-Language': 'en-US,en;q=0.9',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Cookie': 'sp-cdn="L5Z9:US";'
}
BASE_URL = "https://www.amazon.com"
TEST_URL = "https://www.amazon.com/s?i=arts-crafts&rh=n%3A12896091%2Cp_36%3A-1000%2Cp_76%3A2661625011"
TEST_CATEGORY_ID = 12896091
MAX_PRODUCTS_TO_TEST = 10  # 增加处理数量

# --- 2. 数据库操作模块 ---
def get_db_connection():
    """建立并返回数据库连接 (使用PyMySQL)"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        print("✅ 数据库连接成功 (使用 PyMySQL)！")
        return conn
    except pymysql.MySQLError as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def save_page_task_to_db(cursor, task_data):
    """将列表页抓取的任务数据存入 zz_amazon_page_tasks 表"""
    sql = """
        INSERT INTO zz_amazon_page_tasks (
            entry_asin, url, status, list_page_title, list_page_price, 
            list_page_rating, list_page_review_count, list_page_main_image_url, 
            list_page_prime_info, is_sponsored
        )
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            url = VALUES(url),
            list_page_title = VALUES(list_page_title),
            list_page_price = VALUES(list_page_price),
            list_page_rating = VALUES(list_page_rating),
            list_page_review_count = VALUES(list_page_review_count),
            list_page_main_image_url = VALUES(list_page_main_image_url),
            list_page_prime_info = VALUES(list_page_prime_info),
            is_sponsored = VALUES(is_sponsored),
            updated_at = CURRENT_TIMESTAMP
    """
    cursor.execute(sql, (
        task_data['entry_asin'],
        task_data['url'],
        task_data.get('status', 'pending'),
        task_data.get('list_page_title'),
        task_data.get('list_page_price'),
        task_data.get('list_page_rating'),
        task_data.get('list_page_review_count'),
        task_data.get('list_page_main_image_url'),
        task_data.get('list_page_prime_info'),
        task_data.get('is_sponsored', 0)
    ))
    print(f"      ✅ 任务数据已存入数据库: ASIN {task_data['entry_asin']}")

# --- 3. 列表页解析模块 ---
def parse_search_results_from_url(url):
    """从实际的搜索URL获取并解析商品列表信息"""
    print(f"\n[步骤1] 正在请求搜索URL: {url}")

    try:
        # 首先尝试使用本地HTML文件进行测试
        try:
            with open('doc/list.html', 'r', encoding='utf-8') as f:
                html_content = f.read()
            print(f"   使用本地HTML文件进行测试")
            soup = BeautifulSoup(html_content, 'html.parser')
        except FileNotFoundError:
            # 如果本地文件不存在，则请求URL
            response = requests.get(url, headers=HEADERS, timeout=20)
            response.raise_for_status()
            print(f"   网页获取成功，状态码: {response.status_code}")
            soup = BeautifulSoup(response.text, 'html.parser')
        products = []
        
        # 根据实际HTML结构优化商品容器选择器策略
        # 首先查找主要的搜索结果容器
        search_results_container = soup.select_one('span[data-component-type="s-search-results"]')

        product_containers = []
        used_selector = None

        if search_results_container:
            print(f"   找到搜索结果主容器")
            # 在主容器内查找商品卡片 - 尝试多种选择器
            container_selectors = [
                'div[role="listitem"][data-asin]',  # 主要选择器：有role和asin的div
                'div[role="listitem"]',  # 所有listitem（包括没有直接data-asin的）
                'div[data-asin]',  # 所有有data-asin的div
                '[data-component-type="s-search-result"]',  # 搜索结果组件
                'div.s-result-item',  # 结果项目类
                'div[data-uuid][data-index]',  # 有UUID和index的div
                'div[data-cel-widget]'  # 有cel-widget的div
            ]

            for selector in container_selectors:
                containers = search_results_container.select(selector)
                if containers:
                    print(f"   使用选择器 '{selector}' 在主容器内找到 {len(containers)} 个容器")
                    # 过滤出有ASIN的容器
                    valid_containers = []
                    for container in containers:
                        asin = container.get('data-asin')
                        if asin:
                            valid_containers.append(container)
                        else:
                            # 检查子元素是否有ASIN，或者检查其他可能的ASIN来源
                            asin_elem = container.select_one('[data-asin]')
                            if asin_elem:
                                valid_containers.append(container)
                            else:
                                # 检查是否有data-uuid和data-index，这些通常表示是商品容器
                                uuid = container.get('data-uuid')
                                index = container.get('data-index')
                                if uuid and index:
                                    # 尝试从子元素的链接中提取ASIN
                                    link_elem = container.select_one('a[href*="/dp/"]')
                                    if link_elem:
                                        href = link_elem.get('href', '')
                                        asin_match = re.search(r'/dp/([A-Z0-9]{10})', href)
                                        if asin_match:
                                            valid_containers.append(container)

                    if valid_containers:
                        product_containers = valid_containers
                        used_selector = f"{selector} (within s-search-results, {len(valid_containers)} valid)"
                        print(f"   过滤后得到 {len(valid_containers)} 个有效容器")
                        break

        # 如果主容器方法失败，使用传统方法
        if not product_containers:
            print("   主容器方法失败，尝试传统选择器...")
            traditional_selectors = [
                'div[role="listitem"][data-asin]',
                'span[data-component-type="s-search-result"]',
                'div[data-component-type="s-search-result"]'
            ]

            for selector in traditional_selectors:
                containers = soup.select(selector)
                if containers:
                    print(f"   使用传统选择器 '{selector}' 找到 {len(containers)} 个容器")
                    product_containers = containers
                    used_selector = selector
                    break

        # 最后的备用方法
        if not product_containers:
            print("   所有选择器都失败，尝试通用方法...")
            # 查找所有包含 data-asin 属性的元素
            product_containers = soup.find_all(attrs={"data-asin": True})
            if product_containers:
                print(f"   通过 data-asin 属性找到 {len(product_containers)} 个容器")
                used_selector = "[data-asin] (generic)"
        
        print(f"   最终找到 {len(product_containers)} 个商品容器")
        
        # 调试：输出所有找到容器的基本信息
        print(f"\n=== 容器详情 ===")
        for i, container in enumerate(product_containers):
            asin = container.get('data-asin') or container.get('data-uuid')
            title_elem = container.select_one('h2 a span, h2 span, [data-cy="title-recipe-title"]')
            title = title_elem.get_text(strip=True)[:50] if title_elem else "无标题"
            print(f"容器 {i+1}: ASIN={asin}, 标题={title}...")
        print(f"==================")
        
        print(f"\n=== 开始解析商品 ===")
        for i, container in enumerate(product_containers):
            try:
                print(f"\n--- 解析第 {i+1} 个商品 ---")
                product = parse_single_product_from_list(container)
                if product and product.get('entry_asin'):
                    products.append(product)
                    print(f"✓ 商品 {i+1} 解析成功")
                else:
                    print(f"✗ 商品 {i+1} 解析失败")
                    # 调试输出为什么跳过
                    if not product:
                        print(f"     原因：解析返回None")
                    elif not product.get('entry_asin'):
                        print(f"     原因：未找到ASIN")
            except Exception as e:
                print(f"   ❌ 解析第 {i+1} 个商品失败: {e}")
                continue
        
        print(f"\n=== 解析完成 ===")
        print(f"   成功解析 {len(products)} 个有效商品")
        
        # 输出所有成功解析的商品摘要
        print(f"\n=== 商品摘要 ===")
        for i, product in enumerate(products):
            print(f"{i+1}. ASIN: {product['entry_asin']}, 标题: {product['list_page_title'][:50]}...")
        print(f"==================")
        
        return products
        
    except Exception as e:
        print(f"❌ 请求搜索URL失败: {e}")
        return []

def parse_single_product_from_list(container):
    """从单个商品容器中解析数据"""
    product = {}
    
    try:
        # 增强ASIN提取逻辑
        asin = None
        asin_sources = [
            container.get('data-asin'),
            container.get('data-uuid'),
            container.get('data-index'),  # 有些商品可能使用data-index
        ]
        
        for source in asin_sources:
            if source and source.strip():
                asin = source.strip()
                break
        
        # 如果还没找到ASIN，尝试从子元素中查找
        if not asin:
            asin_elem = container.select_one('[data-asin], [data-uuid]')
            if asin_elem:
                asin = asin_elem.get('data-asin') or asin_elem.get('data-uuid', '').strip()
        
        # 如果还是没有ASIN，尝试从链接中提取
        if not asin:
            link_elem = container.select_one('a[href*="/dp/"], a[href*="/gp/product/"]')
            if link_elem:
                href = link_elem.get('href', '')
                asin_match = re.search(r'/dp/([A-Z0-9]{10})', href)
                if asin_match:
                    asin = asin_match.group(1)
        
        if not asin:
            return None
            
        product['entry_asin'] = asin
        
        # 构建详情页URL
        product['url'] = f"{BASE_URL}/dp/{asin}"
        
        # 根据实际HTML结构优化标题提取逻辑
        title_selectors = [
            # 主要选择器 - 基于实际HTML结构
            'h2 span',  # 标题在h2内的span中
            'h2 a span',  # 有些标题在链接内的span中
            '[data-cy="title-recipe"] h2 span',  # 更具体的路径
            '.s-title-instructions-style h2 span',
            # 备用选择器
            'h2 a span[aria-label]',
            '[data-cy="title-recipe-title"]',
            '.a-size-base-plus span',
            '.a-size-medium span',
            '.a-size-base span',
            'h2 a',
            'a[aria-label]',
            'a[title]'
        ]
        
        title = None
        for selector in title_selectors:
            title_elem = container.select_one(selector)
            if title_elem:
                title_text = title_elem.get_text(strip=True)
                # 检查是否为有意义的标题
                if title_text and len(title_text) > 5 and not title_text.isdigit():
                    title = title_text
                    break
        
        # 如果还没找到标题，尝试从aria-label或title属性获取
        if not title:
            link_elem = container.select_one('h2 a, a[aria-label], a[title]')
            if link_elem:
                title = (link_elem.get('aria-label') or 
                        link_elem.get('title') or '').strip()
        
        product['list_page_title'] = title[:512] if title else None
        
        # === 打印详细信息 ===
        print(f"\n=== 商品信息 ===")
        print(f"ASIN: {asin}")
        print(f"URL: {product['url']}")
        print(f"标题: {title}")

        # 调试：检查容器的基本属性
        print(f"容器类名: {container.get('class', [])}")
        print(f"data-index: {container.get('data-index')}")
        print(f"data-uuid: {container.get('data-uuid')}")
        print(f"==================")
        
        # 提取价格 - 增强选择器
        price = extract_price_from_container(container)
        product['list_page_price'] = price
        
        # 提取评分
        rating = extract_rating_from_container(container)
        product['list_page_rating'] = rating
        
        # 提取评价数量
        review_count = extract_review_count_from_container(container)
        product['list_page_review_count'] = review_count
        
        # 提取主图URL
        image_url = extract_image_url_from_container(container)
        product['list_page_main_image_url'] = image_url
        
        # 提取Prime配送信息 - 修复deprecated warning
        prime_info = extract_prime_info_from_container(container)
        product['list_page_prime_info'] = prime_info
        
        # 检查是否为广告
        is_sponsored = check_if_sponsored(container)
        product['is_sponsored'] = 1 if is_sponsored else 0
        
        product['status'] = 'pending'
        
        return product
        
    except Exception as e:
        print(f"   解析单个商品出错: {e}")
        return None

def extract_price_from_container(container):
    """从容器中提取价格"""
    price_selectors = [
        # 主要价格选择器 - 基于实际HTML结构
        '.a-price .a-offscreen',  # 主要价格在a-price内的a-offscreen中
        '[data-cy="price-recipe"] .a-price .a-offscreen',  # 更具体的路径
        '.s-price-instructions-style .a-price .a-offscreen',
        # 备用选择器
        '.a-price-whole',
        '[data-component-type="s-product-price"] .a-price .a-offscreen',
        '.a-price-symbol + .a-price-whole',
        '.a-price-range .a-offscreen',
        'span.a-price-range'
    ]
    
    for selector in price_selectors:
        price_elems = container.select(selector)
        for price_elem in price_elems:
            if price_elem:
                price_text = price_elem.get_text(strip=True)
                # 提取数字价格
                price_match = re.search(r'\$?(\d+\.?\d*)', price_text.replace(',', ''))
                if price_match:
                    try:
                        return float(price_match.group(1))
                    except ValueError:
                        continue
    return None

def extract_rating_from_container(container):
    """从容器中提取评分"""
    rating_selectors = [
        # 主要评分选择器 - 基于实际HTML结构
        '.a-icon-star-mini .a-icon-alt',  # 评分在star-mini图标的alt属性中
        '[data-cy="reviews-block"] .a-icon-alt',  # 在reviews-block内的图标
        '.mvt-review-star-mini .a-icon-alt',
        # 备用选择器
        '.a-icon-alt',
        '[aria-label*="out of"]',
        '.a-star-medium .a-icon-alt',
        '.a-star-small .a-icon-alt',
        'span[aria-label*="stars"]'
    ]
    
    for selector in rating_selectors:
        rating_elem = container.select_one(selector)
        if rating_elem:
            rating_text = rating_elem.get('aria-label') or rating_elem.get_text()
            if rating_text:
                rating_match = re.search(r'(\d+\.?\d*)\s*out of', rating_text)
                if rating_match:
                    try:
                        return float(rating_match.group(1))
                    except ValueError:
                        continue
    return None

def extract_review_count_from_container(container):
    """从容器中提取评价数量"""
    review_selectors = [
        # 主要评价数量选择器 - 基于实际HTML结构
        '[data-cy="reviews-block"] a span',  # 在reviews-block内的链接span中
        'a[href*="#customerReviews"] span',  # 指向customerReviews的链接
        '.s-underline-text',  # 评价数量通常有下划线样式
        '[data-csa-c-content-id="alf-customer-ratings-count-component"] span',
        # 备用选择器
        '.a-size-base[data-component-type="s-client-side-analytics"]',
        'span[aria-label*="stars"] + a span',
        'a span[aria-label]'
    ]
    
    for selector in review_selectors:
        review_elem = container.select_one(selector)
        if review_elem:
            review_text = review_elem.get_text(strip=True)
            # 查找数字（可能带逗号）
            review_match = re.search(r'([\d,]+)', review_text)
            if review_match:
                try:
                    return int(review_match.group(1).replace(',', ''))
                except ValueError:
                    continue
    return None

def extract_image_url_from_container(container):
    """从容器中提取图片URL"""
    img_elem = container.select_one('img')
    if img_elem:
        # 尝试获取高质量图片URL
        img_src = img_elem.get('src') or img_elem.get('data-src')
        if img_src:
            # 如果是Amazon的图片，尝试获取更高质量版本
            if any(domain in img_src for domain in ['images-na.ssl-images-amazon.com', 'm.media-amazon.com']):
                # 替换为更大尺寸
                img_src = re.sub(r'_AC_[^.]*_\.', '_AC_SX300_SY300_QL70_.', img_src)
            return img_src
    return None

def extract_prime_info_from_container(container):
    """从容器中提取Prime配送信息"""
    # 根据实际HTML结构优化配送信息提取
    prime_texts = []

    # 主要配送信息选择器
    delivery_selectors = [
        '[data-cy="delivery-recipe"]',  # 配送信息主容器
        '.udm-delivery-block',  # 配送块
        '.udm-primary-delivery-message',  # 主要配送信息
        '.a-icon-prime-with-text',  # Prime图标和文本
        '[data-component-type="s-delivery-message"]'
    ]

    # 尝试从特定的配送信息容器中提取
    for selector in delivery_selectors:
        delivery_elem = container.select_one(selector)
        if delivery_elem:
            delivery_text = delivery_elem.get_text(strip=True)
            if delivery_text and len(delivery_text) < 200:
                prime_texts.append(delivery_text)
                break

    # 如果没有找到，使用文本搜索方法
    if not prime_texts:
        try:
            all_elements = container.find_all(string=re.compile(r'(delivery|Prime|shipping|arrives|FREE)', re.I))
            for elem in all_elements:
                text = elem.strip()
                if text and len(text) < 100:  # 避免过长的文本
                    prime_texts.append(text)
        except Exception:
            # 如果新方法失败，使用旧方法
            try:
                all_elements = container.find_all(text=re.compile(r'(delivery|Prime|shipping|arrives|FREE)', re.I))
                for elem in all_elements:
                    text = elem.strip()
                    if text and len(text) < 100:
                        prime_texts.append(text)
            except Exception:
                pass

    if prime_texts:
        return ' | '.join(prime_texts[:2])  # 最多取前两个信息

    return None

def check_if_sponsored(container):
    """检查商品是否为广告"""
    sponsored_indicators = [
        'sponsored',
        'ad',
        '[data-component-type="sp-sponsored-result"]'
    ]
    
    container_text = container.get_text().lower()
    container_html = str(container).lower()
    
    for indicator in sponsored_indicators:
        if indicator in container_text or indicator in container_html:
            return True
    
    return False

# --- 4. 主执行流程 ---
def main():
    """主执行函数"""
    print("--- 亚马逊列表页数据提取并存入任务队列 ---")
    
    db_conn = get_db_connection()
    if not db_conn:
        return
    
    try:
        with db_conn.cursor() as cursor:
            # [步骤1] 从实际搜索URL获取数据
            products = parse_search_results_from_url(TEST_URL)
            
            if not products:
                raise Exception("未能从搜索URL中解析出任何商品数据")
            
            # [步骤2] 将商品数据存入任务队列
            print(f"\n[步骤2] 正在将 {len(products)} 个商品存入任务队列...")
            
            success_count = 0
            for i, product in enumerate(products):
                try:
                    print(f"   处理商品 {i+1}/{len(products)}: {product.get('list_page_title', 'Unknown')[:30]}...")
                    save_page_task_to_db(cursor, product)
                    success_count += 1
                except Exception as e:
                    print(f"   ❌ 保存商品 {i+1} 失败: {e}")
                    continue
            
            db_conn.commit()
            print(f"\n✅ 成功将 {success_count}/{len(products)} 个商品存入任务队列")
            
            # [步骤3] 显示统计信息
            cursor.execute("SELECT COUNT(*) as total FROM zz_amazon_page_tasks WHERE status = 'pending'")
            pending_count = cursor.fetchone()[0]
            print(f"📊 当前待处理任务数: {pending_count}")
                
    except Exception as e:
        print(f"\n❌ 处理过程中发生错误: {e}")
        if db_conn.open:
            db_conn.rollback()
    finally:
        if db_conn.open:
            db_conn.close()
            print("\n--- 数据库连接已关闭，处理完成 ---")

# ...existing code for detail page scraping functions...

if __name__ == '__main__':
    main()