2025-06-15 08:23:00,866 - root - INFO - [MainThread] - 这是一条信息日志
2025-06-15 08:23:00,867 - root - WARNING - [MainThread] - 这是一条警告日志
2025-06-15 08:23:00,867 - root - ERROR - [MainThread] - 这是一条错误日志
2025-06-15 08:23:01,254 - proxy_manager - INFO - [MainThread] - 已从数据库加载 100 个代理
2025-06-15 08:23:09,928 - root - INFO - [MainThread] - 验证配置...
2025-06-15 08:23:09,928 - root - INFO - [MainThread] - 配置验证通过
2025-06-15 08:23:17,270 - root - INFO - [MainThread] - Amazon爬虫管理器启动
2025-06-15 08:23:17,271 - root - INFO - [MainThread] - 验证运行环境...
2025-06-15 08:23:17,319 - root - INFO - [MainThread] - 数据库连接测试成功
2025-06-15 08:23:17,386 - root - WARNING - [MainThread] - 代理文件不存在: proxies.txt
2025-06-15 08:23:17,386 - root - INFO - [MainThread] - 环境验证通过
2025-06-15 08:23:17,452 - root - INFO - [MainThread] - 发现 2761 个待处理任务
2025-06-15 08:23:17,452 - root - INFO - [MainThread] - 干运行模式，不执行实际抓取
2025-06-15 08:27:53,146 - root - INFO - [MainThread] - Amazon多线程爬虫系统启动
2025-06-15 08:27:53,147 - root - INFO - [MainThread] - 配置信息: {'max_workers': 5, 'request_timeout': 30, 'retry_count': 3, 'delay_range': (2, 5), 'page_delay_range': (3, 8), 'use_proxy': True, 'rotate_user_agent': True}
2025-06-15 08:27:57,462 - proxy_manager - ERROR - [MainThread] - 从数据库加载代理出错: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (10061)
2025-06-15 08:27:57,462 - AmazonCrawler - INFO - [MainThread] - 代理管理器初始化完成
2025-06-15 08:27:57,462 - AmazonCrawler - INFO - [MainThread] - 启动Amazon爬虫系统
2025-06-15 08:28:01,561 - AmazonCrawler - ERROR - [MainThread] - 加载任务失败: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] 由于目标计算机积极拒绝，无法连接。)")
2025-06-15 08:28:01,561 - AmazonCrawler - INFO - [MainThread] - 没有待处理的任务
2025-06-15 08:28:01,562 - root - INFO - [MainThread] - 程序结束
