#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Amazon列表页分页爬虫
支持多页抓取、断点续传、任务管理
"""

import requests
import pymysql
import json
import re
import time
import random
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse, parse_qs, urlencode, urlunparse
from decimal import Decimal
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('amazon_pagination_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 请求头配置
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Accept-Encoding': 'gzip, deflate',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'database': 'amazon_crawler',
    'charset': 'utf8mb4'
}

def get_db_connection():
    """获取数据库连接"""
    return pymysql.connect(**DB_CONFIG)

def extract_next_page_url(soup, current_url):
    """
    从HTML中提取下一页URL
    
    Args:
        soup: BeautifulSoup对象
        current_url: 当前页面URL
    
    Returns:
        str: 下一页URL，如果没有下一页返回None
    """
    try:
        # 查找下一页链接 - 基于分析的HTML结构
        next_link = soup.select_one('a.s-pagination-next')
        
        if next_link and next_link.get('href'):
            next_url = next_link['href']
            # 处理相对URL
            if next_url.startswith('/'):
                base_url = f"{urlparse(current_url).scheme}://{urlparse(current_url).netloc}"
                next_url = urljoin(base_url, next_url)
            
            logger.info(f"找到下一页URL: {next_url}")
            return next_url
        
        # 备用方案：通过修改page参数构建下一页URL
        return build_next_page_url_by_param(current_url)
        
    except Exception as e:
        logger.error(f"提取下一页URL失败: {e}")
        return None

def build_next_page_url_by_param(current_url):
    """
    通过修改URL参数构建下一页URL（备用方案）
    
    Args:
        current_url: 当前页面URL
    
    Returns:
        str: 下一页URL
    """
    try:
        parsed = urlparse(current_url)
        params = parse_qs(parsed.query)
        
        # 获取当前页码
        current_page = int(params.get('page', ['1'])[0])
        next_page = current_page + 1
        
        # 更新page参数
        params['page'] = [str(next_page)]
        
        # 重建URL
        new_query = urlencode(params, doseq=True)
        new_url = urlunparse((
            parsed.scheme, parsed.netloc, parsed.path,
            parsed.params, new_query, parsed.fragment
        ))
        
        logger.info(f"通过参数构建下一页URL: {new_url}")
        return new_url
        
    except Exception as e:
        logger.error(f"通过参数构建下一页URL失败: {e}")
        return None

def get_pagination_info(soup):
    """
    获取分页信息
    
    Args:
        soup: BeautifulSoup对象
    
    Returns:
        dict: 包含当前页、总页数等信息
    """
    try:
        pagination_info = {
            'current_page': 1,
            'total_pages': 1,
            'has_next': False
        }
        
        # 获取当前页码
        current_page_elem = soup.select_one('.s-pagination-selected')
        if current_page_elem:
            pagination_info['current_page'] = int(current_page_elem.get_text(strip=True))
        
        # 检查是否有下一页
        next_link = soup.select_one('a.s-pagination-next')
        pagination_info['has_next'] = bool(next_link and next_link.get('href'))
        
        # 尝试获取总页数（从最后一个页码链接）
        page_links = soup.select('.s-pagination-item')
        max_page = 1
        for link in page_links:
            text = link.get_text(strip=True)
            if text.isdigit():
                max_page = max(max_page, int(text))
        pagination_info['total_pages'] = max_page
        
        logger.info(f"分页信息: {pagination_info}")
        return pagination_info
        
    except Exception as e:
        logger.error(f"获取分页信息失败: {e}")
        return {'current_page': 1, 'total_pages': 1, 'has_next': False}

def create_list_task(url, category_id, max_pages=5):
    """
    创建列表页抓取任务
    
    Args:
        url: 列表页URL（第一页）
        category_id: 类目ID
        max_pages: 最大抓取页数
    
    Returns:
        int: 任务ID，失败返回None
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查是否已存在该类目的任务
        cursor.execute(
            "SELECT id, status FROM zz_amazon_list_tasks WHERE source_category_id = %s",
            (category_id,)
        )
        existing_task = cursor.fetchone()
        
        if existing_task:
            task_id, status = existing_task
            if status in ['pending', 'in_progress']:
                logger.info(f"类目 {category_id} 已有进行中的任务 {task_id}")
                return task_id
            else:
                # 重置已完成或失败的任务
                cursor.execute(
                    """UPDATE zz_amazon_list_tasks 
                       SET url = %s, max_pages_to_crawl = %s, crawled_pages = 0, 
                           status = 'pending', error_message = NULL, updated_at = NOW()
                       WHERE id = %s""",
                    (url, max_pages, task_id)
                )
                conn.commit()
                logger.info(f"重置任务 {task_id} 为新的抓取任务")
                return task_id
        else:
            # 创建新任务
            cursor.execute(
                """INSERT INTO zz_amazon_list_tasks 
                   (url, source_category_id, max_pages_to_crawl, status) 
                   VALUES (%s, %s, %s, 'pending')""",
                (url, category_id, max_pages)
            )
            task_id = cursor.lastrowid
            conn.commit()
            logger.info(f"创建新任务 {task_id}，类目 {category_id}，最大页数 {max_pages}")
            return task_id
            
    except Exception as e:
        logger.error(f"创建任务失败: {e}")
        if 'conn' in locals():
            conn.rollback()
        return None
    finally:
        if 'conn' in locals():
            conn.close()

def update_task_progress(task_id, crawled_pages=None, status=None, error_msg=None):
    """
    更新任务进度
    
    Args:
        task_id: 任务ID
        crawled_pages: 已抓取页数
        status: 任务状态
        error_msg: 错误信息
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        update_fields = []
        update_values = []
        
        if crawled_pages is not None:
            update_fields.append("crawled_pages = %s")
            update_values.append(crawled_pages)
        
        if status is not None:
            update_fields.append("status = %s")
            update_values.append(status)
        
        if error_msg is not None:
            update_fields.append("error_message = %s")
            update_values.append(error_msg)
        
        if update_fields:
            update_fields.append("updated_at = NOW()")
            sql = f"UPDATE zz_amazon_list_tasks SET {', '.join(update_fields)} WHERE id = %s"
            update_values.append(task_id)
            
            cursor.execute(sql, update_values)
            conn.commit()
            logger.info(f"更新任务 {task_id} 进度: 页数={crawled_pages}, 状态={status}")
            
    except Exception as e:
        logger.error(f"更新任务进度失败: {e}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'conn' in locals():
            conn.close()

def crawl_single_list_page(url, retry_count=3):
    """
    抓取单个列表页

    Args:
        url: 页面URL
        retry_count: 重试次数

    Returns:
        tuple: (soup对象, 商品数据列表)
    """
    for attempt in range(retry_count):
        try:
            logger.info(f"正在抓取页面: {url} (尝试 {attempt + 1}/{retry_count})")

            # 随机延迟，避免被封IP
            time.sleep(random.uniform(1, 3))

            response = requests.get(url, headers=HEADERS, timeout=30)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')

            # 检查是否被反爬虫拦截
            if "Robot Check" in response.text or "captcha" in response.text.lower():
                logger.warning("检测到反爬虫验证，需要处理")
                time.sleep(random.uniform(5, 10))
                continue

            # 提取商品数据
            products = extract_products_from_list_page(soup)
            logger.info(f"成功抓取页面，找到 {len(products)} 个商品")

            return soup, products

        except requests.RequestException as e:
            logger.error(f"请求失败 (尝试 {attempt + 1}/{retry_count}): {e}")
            if attempt < retry_count - 1:
                time.sleep(random.uniform(3, 6))
        except Exception as e:
            logger.error(f"抓取页面失败 (尝试 {attempt + 1}/{retry_count}): {e}")
            if attempt < retry_count - 1:
                time.sleep(random.uniform(2, 4))

    logger.error(f"抓取页面失败，已重试 {retry_count} 次: {url}")
    return None, []

def extract_products_from_list_page(soup):
    """
    从列表页提取商品数据

    Args:
        soup: BeautifulSoup对象

    Returns:
        list: 商品数据列表
    """
    products = []

    try:
        # 查找商品容器 - 基于之前的分析
        product_containers = soup.select('[data-component-type="s-search-result"]')

        logger.info(f"找到 {len(product_containers)} 个商品容器")

        for container in product_containers:
            try:
                product_data = extract_single_product_from_container(container)
                if product_data:
                    products.append(product_data)
            except Exception as e:
                logger.error(f"提取单个商品数据失败: {e}")
                continue

        logger.info(f"成功提取 {len(products)} 个商品数据")

    except Exception as e:
        logger.error(f"提取商品数据失败: {e}")

    return products

def extract_single_product_from_container(container):
    """
    从商品容器中提取单个商品数据

    Args:
        container: 商品容器元素

    Returns:
        dict: 商品数据
    """
    try:
        product_data = {}

        # 提取ASIN
        asin = container.get('data-asin', '')
        if not asin:
            return None
        product_data['asin'] = asin

        # 提取标题
        title_elem = container.select_one('h2 a span, [data-cy="title-recipe-title"]')
        product_data['title'] = title_elem.get_text(strip=True) if title_elem else ''

        # 提取价格
        price_elem = container.select_one('.a-price-whole, .a-price .a-offscreen')
        if price_elem:
            price_text = price_elem.get_text(strip=True)
            # 清理价格文本，提取数字
            price_match = re.search(r'[\d,]+\.?\d*', price_text.replace(',', ''))
            if price_match:
                try:
                    product_data['price'] = Decimal(price_match.group())
                except:
                    product_data['price'] = Decimal('0.00')
            else:
                product_data['price'] = Decimal('0.00')
        else:
            product_data['price'] = Decimal('0.00')

        # 提取图片URL
        img_elem = container.select_one('img')
        product_data['image_url'] = img_elem.get('src', '') if img_elem else ''

        # 提取商品链接
        link_elem = container.select_one('h2 a, [data-cy="title-recipe-title"]')
        if link_elem and link_elem.get('href'):
            product_data['product_url'] = urljoin('https://www.amazon.com', link_elem['href'])
        else:
            product_data['product_url'] = ''

        # 提取评分
        rating_elem = container.select_one('.a-icon-alt')
        if rating_elem:
            rating_text = rating_elem.get_text(strip=True)
            rating_match = re.search(r'(\d+\.?\d*)', rating_text)
            if rating_match:
                try:
                    product_data['rating'] = Decimal(rating_match.group(1))
                except:
                    product_data['rating'] = Decimal('0.0')
            else:
                product_data['rating'] = Decimal('0.0')
        else:
            product_data['rating'] = Decimal('0.0')

        # 提取评论数
        review_elem = container.select_one('.a-size-base')
        if review_elem:
            review_text = review_elem.get_text(strip=True)
            review_match = re.search(r'([\d,]+)', review_text.replace(',', ''))
            if review_match:
                try:
                    product_data['review_count'] = int(review_match.group(1))
                except:
                    product_data['review_count'] = 0
            else:
                product_data['review_count'] = 0
        else:
            product_data['review_count'] = 0

        # 添加抓取时间
        product_data['crawled_at'] = datetime.now()

        return product_data

    except Exception as e:
        logger.error(f"提取商品数据失败: {e}")
        return None

def crawl_list_pages_with_pagination(task_id):
    """
    执行分页抓取任务

    Args:
        task_id: 任务ID

    Returns:
        bool: 抓取是否成功
    """
    try:
        # 获取任务信息
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute(
            """SELECT url, source_category_id, max_pages_to_crawl, crawled_pages, status
               FROM zz_amazon_list_tasks WHERE id = %s""",
            (task_id,)
        )
        task_info = cursor.fetchone()

        if not task_info:
            logger.error(f"任务 {task_id} 不存在")
            return False

        base_url, category_id, max_pages, crawled_pages, status = task_info

        if status not in ['pending', 'in_progress']:
            logger.info(f"任务 {task_id} 状态为 {status}，跳过")
            return True

        # 更新任务状态为进行中
        update_task_progress(task_id, status='in_progress')

        logger.info(f"开始执行任务 {task_id}，类目 {category_id}，最大页数 {max_pages}，已抓取 {crawled_pages} 页")

        # 从已抓取页数的下一页开始
        current_page = crawled_pages + 1
        current_url = base_url

        # 如果不是从第一页开始，需要构建当前页的URL
        if current_page > 1:
            current_url = build_page_url(base_url, current_page)

        total_products = 0

        while current_page <= max_pages:
            logger.info(f"正在抓取第 {current_page} 页，URL: {current_url}")

            # 抓取当前页
            soup, products = crawl_single_list_page(current_url)

            if soup is None:
                error_msg = f"抓取第 {current_page} 页失败"
                logger.error(error_msg)
                update_task_progress(task_id, crawled_pages=current_page-1, status='failed', error_msg=error_msg)
                return False

            # 保存商品数据
            if products:
                saved_count = save_products_to_db(products, category_id)
                total_products += saved_count
                logger.info(f"第 {current_page} 页保存了 {saved_count} 个商品")

            # 更新已抓取页数
            update_task_progress(task_id, crawled_pages=current_page)

            # 检查是否有下一页
            pagination_info = get_pagination_info(soup)

            if not pagination_info['has_next'] or current_page >= max_pages:
                logger.info(f"已到达最后一页或达到最大页数限制，停止抓取")
                break

            # 获取下一页URL
            next_url = extract_next_page_url(soup, current_url)
            if not next_url:
                logger.warning(f"无法获取下一页URL，停止抓取")
                break

            current_url = next_url
            current_page += 1

            # 页面间隔延迟
            time.sleep(random.uniform(2, 4))

        # 标记任务完成
        update_task_progress(task_id, status='completed')
        logger.info(f"任务 {task_id} 完成，共抓取 {current_page-1} 页，{total_products} 个商品")

        return True

    except Exception as e:
        error_msg = f"执行任务失败: {e}"
        logger.error(error_msg)
        update_task_progress(task_id, status='failed', error_msg=error_msg)
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def build_page_url(base_url, page_number):
    """
    构建指定页码的URL

    Args:
        base_url: 基础URL（第一页）
        page_number: 页码

    Returns:
        str: 指定页码的URL
    """
    try:
        if page_number == 1:
            return base_url

        parsed = urlparse(base_url)
        params = parse_qs(parsed.query)
        params['page'] = [str(page_number)]

        new_query = urlencode(params, doseq=True)
        new_url = urlunparse((
            parsed.scheme, parsed.netloc, parsed.path,
            parsed.params, new_query, parsed.fragment
        ))

        return new_url

    except Exception as e:
        logger.error(f"构建页面URL失败: {e}")
        return base_url

def save_products_to_db(products, category_id):
    """
    保存商品数据到数据库

    Args:
        products: 商品数据列表
        category_id: 类目ID

    Returns:
        int: 保存的商品数量
    """
    if not products:
        return 0

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        saved_count = 0

        for product in products:
            try:
                # 检查商品是否已存在
                cursor.execute(
                    "SELECT id FROM zz_amazon_products WHERE asin = %s",
                    (product['asin'],)
                )
                existing = cursor.fetchone()

                if existing:
                    # 更新现有商品
                    cursor.execute(
                        """UPDATE zz_amazon_products
                           SET title = %s, price = %s, image_url = %s, product_url = %s,
                               rating = %s, review_count = %s, updated_at = %s
                           WHERE asin = %s""",
                        (product['title'], product['price'], product['image_url'],
                         product['product_url'], product['rating'], product['review_count'],
                         product['crawled_at'], product['asin'])
                    )
                else:
                    # 插入新商品
                    cursor.execute(
                        """INSERT INTO zz_amazon_products
                           (asin, title, price, image_url, product_url, rating, review_count,
                            category_id, created_at, updated_at)
                           VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                        (product['asin'], product['title'], product['price'], product['image_url'],
                         product['product_url'], product['rating'], product['review_count'],
                         category_id, product['crawled_at'], product['crawled_at'])
                    )

                saved_count += 1

            except Exception as e:
                logger.error(f"保存商品 {product.get('asin', 'unknown')} 失败: {e}")
                continue

        conn.commit()
        logger.info(f"成功保存 {saved_count} 个商品到数据库")
        return saved_count

    except Exception as e:
        logger.error(f"保存商品数据失败: {e}")
        if 'conn' in locals():
            conn.rollback()
        return 0
    finally:
        if 'conn' in locals():
            conn.close()

def process_pending_tasks():
    """
    处理所有待处理的任务

    Returns:
        dict: 处理结果统计
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取所有待处理的任务
        cursor.execute(
            """SELECT id FROM zz_amazon_list_tasks
               WHERE status IN ('pending', 'in_progress')
               ORDER BY created_at ASC"""
        )
        pending_tasks = cursor.fetchall()

        if not pending_tasks:
            logger.info("没有待处理的任务")
            return {'total': 0, 'success': 0, 'failed': 0}

        logger.info(f"找到 {len(pending_tasks)} 个待处理任务")

        results = {'total': len(pending_tasks), 'success': 0, 'failed': 0}

        for (task_id,) in pending_tasks:
            logger.info(f"开始处理任务 {task_id}")

            success = crawl_list_pages_with_pagination(task_id)

            if success:
                results['success'] += 1
                logger.info(f"任务 {task_id} 处理成功")
            else:
                results['failed'] += 1
                logger.error(f"任务 {task_id} 处理失败")

            # 任务间隔延迟
            time.sleep(random.uniform(5, 10))

        logger.info(f"任务处理完成: 总计 {results['total']}, 成功 {results['success']}, 失败 {results['failed']}")
        return results

    except Exception as e:
        logger.error(f"处理任务失败: {e}")
        return {'total': 0, 'success': 0, 'failed': 0}
    finally:
        if 'conn' in locals():
            conn.close()

def get_task_status(task_id=None, category_id=None):
    """
    获取任务状态

    Args:
        task_id: 任务ID（可选）
        category_id: 类目ID（可选）

    Returns:
        list: 任务状态列表
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        if task_id:
            cursor.execute(
                """SELECT id, url, source_category_id, status, max_pages_to_crawl,
                          crawled_pages, error_message, created_at, updated_at
                   FROM zz_amazon_list_tasks WHERE id = %s""",
                (task_id,)
            )
        elif category_id:
            cursor.execute(
                """SELECT id, url, source_category_id, status, max_pages_to_crawl,
                          crawled_pages, error_message, created_at, updated_at
                   FROM zz_amazon_list_tasks WHERE source_category_id = %s""",
                (category_id,)
            )
        else:
            cursor.execute(
                """SELECT id, url, source_category_id, status, max_pages_to_crawl,
                          crawled_pages, error_message, created_at, updated_at
                   FROM zz_amazon_list_tasks ORDER BY created_at DESC LIMIT 20"""
            )

        tasks = cursor.fetchall()

        task_list = []
        for task in tasks:
            task_dict = {
                'id': task[0],
                'url': task[1],
                'source_category_id': task[2],
                'status': task[3],
                'max_pages_to_crawl': task[4],
                'crawled_pages': task[5],
                'error_message': task[6],
                'created_at': task[7],
                'updated_at': task[8],
                'progress_percentage': round((task[5] / task[4]) * 100, 2) if task[4] > 0 else 0
            }
            task_list.append(task_dict)

        return task_list

    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        return []
    finally:
        if 'conn' in locals():
            conn.close()

# 主函数和测试代码
def main():
    """主函数 - 演示分页抓取功能"""

    print("🚀 Amazon列表页分页爬虫启动")
    print("=" * 60)

    # 示例：创建一个抓取任务
    test_url = "https://www.amazon.com/s?k=Kids%27+Party+Centerpieces&i=toys-and-games&rh=n%3A2528084011%2Cp_76%3A2661625011%2Cp_36%3A-1000&ref=sr_nr_p_36_1"
    test_category_id = 12345
    max_pages = 3

    print(f"📋 创建测试任务:")
    print(f"   URL: {test_url}")
    print(f"   类目ID: {test_category_id}")
    print(f"   最大页数: {max_pages}")

    # 创建任务
    task_id = create_list_task(test_url, test_category_id, max_pages)

    if task_id:
        print(f"✅ 任务创建成功，任务ID: {task_id}")

        # 显示任务状态
        print(f"\n📊 任务状态:")
        tasks = get_task_status(task_id=task_id)
        for task in tasks:
            print(f"   任务ID: {task['id']}")
            print(f"   状态: {task['status']}")
            print(f"   进度: {task['crawled_pages']}/{task['max_pages_to_crawl']} ({task['progress_percentage']}%)")

        # 执行任务
        print(f"\n🔄 开始执行分页抓取...")
        success = crawl_list_pages_with_pagination(task_id)

        if success:
            print(f"✅ 任务执行成功!")
        else:
            print(f"❌ 任务执行失败!")

        # 显示最终状态
        print(f"\n📊 最终任务状态:")
        tasks = get_task_status(task_id=task_id)
        for task in tasks:
            print(f"   任务ID: {task['id']}")
            print(f"   状态: {task['status']}")
            print(f"   进度: {task['crawled_pages']}/{task['max_pages_to_crawl']} ({task['progress_percentage']}%)")
            if task['error_message']:
                print(f"   错误信息: {task['error_message']}")

    else:
        print(f"❌ 任务创建失败!")

def demo_batch_processing():
    """演示批量处理任务"""

    print("\n🔄 批量处理演示")
    print("=" * 60)

    # 处理所有待处理任务
    results = process_pending_tasks()

    print(f"📊 批量处理结果:")
    print(f"   总任务数: {results['total']}")
    print(f"   成功: {results['success']}")
    print(f"   失败: {results['failed']}")

def demo_task_management():
    """演示任务管理功能"""

    print("\n📋 任务管理演示")
    print("=" * 60)

    # 显示所有任务状态
    print("📊 所有任务状态:")
    tasks = get_task_status()

    if tasks:
        for task in tasks:
            print(f"   任务 {task['id']}: {task['status']} - {task['crawled_pages']}/{task['max_pages_to_crawl']}页")
            print(f"      类目ID: {task['source_category_id']}")
            print(f"      创建时间: {task['created_at']}")
            if task['error_message']:
                print(f"      错误: {task['error_message']}")
            print()
    else:
        print("   暂无任务")

if __name__ == "__main__":
    try:
        # 运行主演示
        main()

        # 运行任务管理演示
        demo_task_management()

        # 运行批量处理演示（如果有其他待处理任务）
        # demo_batch_processing()

    except KeyboardInterrupt:
        print("\n⏹️ 用户中断程序")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        print(f"❌ 程序执行失败: {e}")

    print("\n🏁 程序结束")

def crawl_single_list_page(url, retry_count=3):
    """
    抓取单个列表页

    Args:
        url: 页面URL
        retry_count: 重试次数

    Returns:
        tuple: (soup对象, 商品数据列表)
    """
    for attempt in range(retry_count):
        try:
            logger.info(f"正在抓取页面: {url} (尝试 {attempt + 1}/{retry_count})")

            # 随机延迟，避免被封IP
            time.sleep(random.uniform(1, 3))

            response = requests.get(url, headers=HEADERS, timeout=30)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')

            # 检查是否被反爬虫拦截
            if "Robot Check" in response.text or "captcha" in response.text.lower():
                logger.warning("检测到反爬虫验证，需要处理")
                time.sleep(random.uniform(5, 10))
                continue

            # 提取商品数据
            products = extract_products_from_list_page(soup)
            logger.info(f"成功抓取页面，找到 {len(products)} 个商品")

            return soup, products

        except requests.RequestException as e:
            logger.error(f"请求失败 (尝试 {attempt + 1}/{retry_count}): {e}")
            if attempt < retry_count - 1:
                time.sleep(random.uniform(3, 6))
        except Exception as e:
            logger.error(f"抓取页面失败 (尝试 {attempt + 1}/{retry_count}): {e}")
            if attempt < retry_count - 1:
                time.sleep(random.uniform(2, 4))

    logger.error(f"抓取页面失败，已重试 {retry_count} 次: {url}")
    return None, []

def extract_products_from_list_page(soup):
    """
    从列表页提取商品数据

    Args:
        soup: BeautifulSoup对象

    Returns:
        list: 商品数据列表
    """
    products = []

    try:
        # 查找商品容器 - 基于之前的分析
        product_containers = soup.select('[data-component-type="s-search-result"]')

        logger.info(f"找到 {len(product_containers)} 个商品容器")

        for container in product_containers:
            try:
                product_data = extract_single_product_from_container(container)
                if product_data:
                    products.append(product_data)
            except Exception as e:
                logger.error(f"提取单个商品数据失败: {e}")
                continue

        logger.info(f"成功提取 {len(products)} 个商品数据")

    except Exception as e:
        logger.error(f"提取商品数据失败: {e}")

    return products

def extract_single_product_from_container(container):
    """
    从商品容器中提取单个商品数据

    Args:
        container: 商品容器元素

    Returns:
        dict: 商品数据
    """
    try:
        product_data = {}

        # 提取ASIN
        asin = container.get('data-asin', '')
        if not asin:
            return None
        product_data['asin'] = asin

        # 提取标题
        title_elem = container.select_one('h2 a span, [data-cy="title-recipe-title"]')
        product_data['title'] = title_elem.get_text(strip=True) if title_elem else ''

        # 提取价格
        price_elem = container.select_one('.a-price-whole, .a-price .a-offscreen')
        if price_elem:
            price_text = price_elem.get_text(strip=True)
            # 清理价格文本，提取数字
            price_match = re.search(r'[\d,]+\.?\d*', price_text.replace(',', ''))
            if price_match:
                try:
                    product_data['price'] = Decimal(price_match.group())
                except:
                    product_data['price'] = Decimal('0.00')
            else:
                product_data['price'] = Decimal('0.00')
        else:
            product_data['price'] = Decimal('0.00')

        # 提取图片URL
        img_elem = container.select_one('img')
        product_data['image_url'] = img_elem.get('src', '') if img_elem else ''

        # 提取商品链接
        link_elem = container.select_one('h2 a, [data-cy="title-recipe-title"]')
        if link_elem and link_elem.get('href'):
            product_data['product_url'] = urljoin('https://www.amazon.com', link_elem['href'])
        else:
            product_data['product_url'] = ''

        # 提取评分
        rating_elem = container.select_one('.a-icon-alt')
        if rating_elem:
            rating_text = rating_elem.get_text(strip=True)
            rating_match = re.search(r'(\d+\.?\d*)', rating_text)
            if rating_match:
                try:
                    product_data['rating'] = Decimal(rating_match.group(1))
                except:
                    product_data['rating'] = Decimal('0.0')
            else:
                product_data['rating'] = Decimal('0.0')
        else:
            product_data['rating'] = Decimal('0.0')

        # 提取评论数
        review_elem = container.select_one('.a-size-base')
        if review_elem:
            review_text = review_elem.get_text(strip=True)
            review_match = re.search(r'([\d,]+)', review_text.replace(',', ''))
            if review_match:
                try:
                    product_data['review_count'] = int(review_match.group(1))
                except:
                    product_data['review_count'] = 0
            else:
                product_data['review_count'] = 0
        else:
            product_data['review_count'] = 0

        # 添加抓取时间
        product_data['crawled_at'] = datetime.now()

        return product_data

    except Exception as e:
        logger.error(f"提取商品数据失败: {e}")
        return None
